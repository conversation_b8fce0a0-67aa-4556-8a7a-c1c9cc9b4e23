[{"prefix": "/admin/ai/agent", "name": "AgentEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/coding", "name": "", "api": [{"method": "get", "path": "/getModuleTree"}, {"method": "post", "path": "/createCode"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/comm", "name": "", "api": [{"method": "post", "path": "/personUpdate"}, {"method": "get", "path": "/uploadMode"}, {"method": "get", "path": "/permmenu"}, {"method": "get", "path": "/program"}, {"method": "get", "path": "/person"}, {"method": "post", "path": "/upload"}, {"method": "post", "path": "/logout"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/open", "name": "", "api": [{"method": "get", "path": "/refreshToken"}, {"method": "get", "path": "/captcha"}, {"method": "post", "path": "/login"}, {"method": "get", "path": "/html"}, {"method": "get", "path": "/eps"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/department", "name": "BaseSysDepartmentEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/order"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/log", "name": "BaseSysLogEntity", "api": [{"method": "post", "path": "/setKeep"}, {"method": "get", "path": "/getKeep"}, {"method": "post", "path": "/clear"}, {"method": "post", "path": "/page"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "姓名", "nullable": true, "source": "b.name"}, {"propertyName": "action", "type": "string", "length": "", "comment": "行为", "nullable": false, "source": "a.action"}, {"propertyName": "ip", "type": "string", "length": "", "comment": "ip", "nullable": true, "source": "a.ip"}]}}, {"prefix": "/admin/base/sys/menu", "name": "BaseSysMenuEntity", "api": [{"method": "post", "path": "/create"}, {"method": "post", "path": "/export"}, {"method": "post", "path": "/import"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/parse"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/base/sys/param", "name": "BaseSysParamEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/html"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "dataType", "type": "number", "length": "", "comment": "数据类型 0-字符串 1-富文本 2-文件 ", "nullable": false, "defaultValue": 0, "source": "a.dataType"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "keyName", "type": "string", "length": "", "comment": "键", "nullable": false, "source": "a.key<PERSON>"}]}}, {"prefix": "/admin/base/sys/role", "name": "BaseSysRoleEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "name", "type": "string", "length": "", "comment": "名称", "nullable": false, "source": "a.name"}, {"propertyName": "label", "type": "string", "length": "50", "comment": "角色标签", "nullable": true, "source": "a.label"}]}}, {"prefix": "/admin/base/sys/user", "name": "BaseSysUserEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/move"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/demo/goods", "name": "DemoGoodsEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["禁用", "启用"], "source": "a.status"}], "fieldLike": [{"propertyName": "title", "type": "string", "length": "50", "comment": "标题", "nullable": false, "source": "a.title"}], "keyWordLikeFields": [{"propertyName": "description", "type": "string", "length": "", "comment": "描述", "nullable": true, "source": "a.description"}]}}, {"prefix": "/admin/demo/tenant", "name": "DemoGoodsEntity", "api": [{"method": "post", "path": "/noTenant"}, {"method": "post", "path": "/noUse"}, {"method": "post", "path": "/use"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/dict/info", "name": "DictInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/types"}, {"method": "post", "path": "/data"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/dict/type", "name": "DictTypeEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/plugin/info", "name": "PluginInfoEntity", "api": [{"method": "post", "path": "/install"}, {"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/recycle/data", "name": "RecycleDataEntity", "api": [{"method": "post", "path": "/restore"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "userName", "type": "string", "length": "", "comment": "姓名", "nullable": true, "source": "b.name"}, {"propertyName": "url", "type": "string", "length": "", "comment": "请求的接口", "nullable": true, "source": "a.url"}]}}, {"prefix": "/admin/space/info", "name": "SpaceInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "type", "type": "string", "length": "", "comment": "类型", "nullable": false, "source": "a.type"}, {"propertyName": "classifyId", "type": "number", "length": "", "comment": "分类ID", "nullable": true, "source": "a.classifyId"}], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/space/type", "name": "SpaceTypeEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/task/info", "name": "TaskInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "post", "path": "/start"}, {"method": "post", "path": "/once"}, {"method": "post", "path": "/stop"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/page"}, {"method": "get", "path": "/log"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态 0-停止 1-运行", "nullable": false, "defaultValue": 1, "source": "a.status"}, {"propertyName": "type", "type": "number", "length": "", "comment": "状态 0-系统 1-用户", "nullable": false, "defaultValue": 0, "source": "a.type"}], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/user/address", "name": "UserAddressEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [], "fieldLike": [], "keyWordLikeFields": []}}, {"prefix": "/admin/user/info", "name": "UserInfoEntity", "api": [{"method": "post", "path": "/delete"}, {"method": "post", "path": "/update"}, {"method": "get", "path": "/info"}, {"method": "post", "path": "/list"}, {"method": "post", "path": "/page"}, {"method": "post", "path": "/add"}], "search": {"fieldEq": [{"propertyName": "status", "type": "number", "length": "", "comment": "状态", "nullable": false, "defaultValue": 1, "dict": ["禁用", "正常", "已注销"], "source": "a.status"}, {"propertyName": "gender", "type": "number", "length": "", "comment": "性别", "nullable": false, "defaultValue": 0, "dict": ["未知", "男", "女"], "source": "a.gender"}, {"propertyName": "loginType", "type": "number", "length": "", "comment": "登录方式", "nullable": false, "defaultValue": 0, "dict": ["小程序", "公众号", "H5"], "source": "a.loginType"}], "fieldLike": [], "keyWordLikeFields": [{"propertyName": "nick<PERSON><PERSON>", "type": "string", "length": "", "comment": "昵称", "nullable": true, "source": "a.nick<PERSON>"}, {"propertyName": "phone", "type": "string", "length": "", "comment": "手机号", "nullable": true, "source": "a.phone"}]}}]