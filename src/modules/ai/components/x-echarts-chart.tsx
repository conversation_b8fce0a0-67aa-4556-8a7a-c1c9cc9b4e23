import { defineComponent } from 'vue';

const props = {
	/** ECharts配置项 */
	options: {
		type: Object,
		default: () => ({})
	},
	/** 图表主题 */
	theme: {
		type: String,
		default: 'light'
	},
	/** 是否显示加载动画 */
	loading: {
		type: Boolean,
		default: false
	}
};

export default defineComponent({
	name: 'x-echarts-chart',
	props,
	setup(props) {
		return () => <v-chart option={props.options} theme={props.theme} autoresize />;
	}
});
