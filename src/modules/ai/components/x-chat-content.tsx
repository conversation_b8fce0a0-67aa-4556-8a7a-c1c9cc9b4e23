import { defineComponent, computed, onMounted, inject, ComputedRef, toRefs, ref, h } from 'vue';
import { useConfig } from 'tdesign-vue-next/es/config-provider/hooks';
import Clipboard from 'clipboard';
import XEchartsChart from './x-echarts-chart';
import hljs from 'highlight.js';
import { Marked } from 'marked';
import { markedHighlight } from 'marked-highlight';
import { usePrefixClass } from 'tdesign-vue-next/es/tree/utils/adapt';
import { NodeToHtml } from '../utils';
import { decode } from 'html-entities';

const escapeTest = /[&<>"']/;
const escapeReplace = new RegExp(escapeTest.source, 'g');
const escapeTestNoEncode = /[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/;
const escapeReplaceNoEncode = new RegExp(escapeTestNoEncode.source, 'g');
type IEscape = {
	[key in '&<>"\'']: string;
};
const escapeReplacements: IEscape = {
	// @ts-ignore
	'&': '&amp;',
	'<': '&lt;',
	'>': '&gt;',
	'"': '&quot;',
	"'": '&#39;'
};
const getEscapeReplacement = (ch: string): string => escapeReplacements[ch as keyof IEscape];

function escape(html: string, encode: boolean = false) {
	if (encode) {
		if (escapeTest.test(html)) {
			return html.replace(escapeReplace, getEscapeReplacement);
		}
	} else if (escapeTestNoEncode.test(html)) {
		return html.replace(escapeReplaceNoEncode, getEscapeReplacement);
	}

	return html;
}
const props = {
	/** 聊天内容，支持 markdown 格式 */
	content: {
		type: String,
		default: ''
	},
	/** 角色，不同选项配置不同的样式，支持类型包括用户、助手、错误、模型切换、系统消息 */
	role: {
		type: String,
		validator(val: string): boolean {
			if (!val) return true;
			return ['user', 'assistant', 'error', 'model-change', 'system'].includes(val);
		}
	},
	isStreamLoad: {
		type: Boolean,
		default: false
	}
};

export default defineComponent({
	name: 't-chat-content',
	components: {
		XEchartsChart
	},
	props,
	setup(props) {
		const COMPONENT_NAME = usePrefixClass('chat');
		const { globalConfig } = useConfig('chat');
		const { copyCodeBtnText, copyCodeSuccessText } = toRefs(globalConfig.value);

		// role 没被注入的时候，使用props.role来自chat-item传入，content在插槽里的inject，修复role数据混乱问题
		const injectedRole = inject<ComputedRef<string>>('role');
		const role = computed(() => props.role || injectedRole?.value || '');
		onMounted(() => {
			const clipboard = new Clipboard(`.${COMPONENT_NAME.value}__copy-btn`, {
				target: (trigger: HTMLDivElement) =>
					(trigger.parentNode as HTMLElement).nextElementSibling as HTMLElement
			});

			clipboard.on('success', e => {
				e.trigger.textContent = copyCodeSuccessText?.value || '';
				setTimeout(() => {
					e.trigger.textContent = copyCodeBtnText?.value || '';
				}, 2000);
				e.clearSelection();
			});
		});

		const echartsOptions = ref<Record<string, any>>({});

		const marked = new Marked(
			markedHighlight({
				highlight(code, language) {
					return hljs.highlightAuto(code, [language]).value;
				}
			}),
			{
				renderer: {
					code(e) {
						const { text, lang, escaped, raw } = e;
						const decodedJsonString = decode(text);
						console.log(decodedJsonString);

						if (lang == 'echarts' && !props.isStreamLoad) {
							const html = NodeToHtml('v-chart', {
								height: '500px',
								width: '500px',
								options: JSON.parse(decodedJsonString)
							});
							console.log(html);
							return html;
						}

						return `<pre class="hljs"><div class="t-chat__code-header">
		      <span class="t-chat__language-txt">${escape(lang || '')}</span>
		      <div class="t-chat__copy-btn" data-clipboard-action="copy">${copyCodeBtnText?.value}</div>
		      </div><code class="hljs language-${escape(lang || '')}" >${escaped ? text : escape(text)}</code></pre>`;
					}
				}
			}
		);

		const getHtmlByMarked = (markdown: string) => {
			if (!markdown) {
				return '<div class="waiting"></div>';
			}
			return marked.parse(markdown);
		};
		const textInfo = computed(() => {
			if (role.value === 'model-change') {
				return props.content || '';
			}

			if (role.value === 'user' && typeof props.content === 'string') {
				return escape(props.content);
			}
			// @ts-ignore 暂时处理
			return getHtmlByMarked(props.content);
		});
		return () => (
			<div class={[`${COMPONENT_NAME.value}__text`]}>
				{role.value === 'user' ? (
					<div class={`${COMPONENT_NAME.value}__text--${role.value}`}>
						<pre v-html={textInfo.value}></pre>
					</div>
				) : (
					<div class={`${COMPONENT_NAME.value}__text__assistant`}>
						<div
							class={[
								`${COMPONENT_NAME.value}__text__content`,
								`${COMPONENT_NAME.value}__text--${role.value}`
							]}
							v-html={textInfo.value}
						></div>
					</div>
				)}
			</div>
		);
	}
});
