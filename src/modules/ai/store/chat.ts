import { defineStore } from 'pinia';
import { ref } from 'vue';

export interface ChatMessage {
    role: 'user' | 'assistant' | 'system';
    content: string;
    avatar?: string;
    name?: string;
    datetime?: string;
    reasoning?: string;
    duration?: number;
}

export const useChatStore = defineStore('ai-chat', function () {
    // 聊天历史记录
    const messages = ref<ChatMessage[]>([]);
    
    // 添加消息
    function addMessage(message: ChatMessage) {
        messages.value.unshift(message);
    }
    
    // 更新最后一条消息
    function updateLastMessage(updates: Partial<ChatMessage>) {
        if (messages.value.length > 0) {
            Object.assign(messages.value[0], updates);
        }
    }
    
    // 获取用于API请求的消息格式
    function getApiMessages() {
        return messages.value
            .filter(msg => msg.role === 'user' || msg.role === 'assistant')
            .map(msg => ({
                role: msg.role,
                content: msg.content
            }))
            .reverse(); // API需要正序
    }
    
    // 清空聊天记录
    function clearMessages() {
        messages.value = [];
    }
    
    return {
        messages,
        addMessage,
        updateLastMessage,
        getApiMessages,
        clearMessages
    };
});