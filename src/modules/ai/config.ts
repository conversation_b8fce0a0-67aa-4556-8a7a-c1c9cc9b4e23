import { type ModuleConfig } from '/@/cool';
export default (): ModuleConfig => {
	return {
		// 页面配置
		pages: [
			{
				path: '/ai/chat',
				component: () => import('./pages/chat/index.vue'),
				meta: {
					label: 'AI 对话'
				}
			},
			{
				path: '/ai/agnetChat',
				component: () => import('./pages/echatrs-agent/index.vue'),
				meta: {
					label: 'AI 对话'
				}
			},
			{
				path: '/ai/test',
				component: () => import('./pages/__test__/chat.vue'),
				meta: {
					label: '测试'
				}
			}
		]
	};
};
