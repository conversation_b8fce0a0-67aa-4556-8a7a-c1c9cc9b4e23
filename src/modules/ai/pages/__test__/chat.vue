<template>
	<div class="box">
		<vue-markdown :markdown>
			<template #code="{ ...props }">
				<v-chart
					:option="handleJson(props.content)"
					autoresize
					style="height: 500px; width: 100%"
				/>
			</template>
		</vue-markdown>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import VueECharts from 'vue-echarts';
import { VueMarkdown } from '@crazydos/vue-markdown';
import { json5ToJsObject } from '/@/utils/json5-to-js-object';

const chartRef = ref<InstanceType<typeof VueECharts>>();
const imageHtml = ref('');
const markdown = ref(`## Hello World
\`\`\`echarts
{
  "title": {
    "text": "2024年GPU性能趋势",
    "left": "center"
  },
  "tooltip": {
    "trigger": "axis",
    "axisPointer": {
      "type": "cross",
      "label": {
        "backgroundColor": "#6a7985"
      }
    }
  },
  "legend": {
    "data": ["GPU性能指数"],
    "top": "bottom"
  },
  "grid": {
    "left": "3%",
    "right": "4%",
    "bottom": "10%",
    "containLabel": true
  },
  "xAxis": {
    "type": "category",
    "boundaryGap": false,
    "data": ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"]
  },
  "yAxis": {
    "type": "value",
    "name": "性能指数",
    "min": 90,
    "max": 125
  },
  "series": [
    {
      "name": "GPU性能指数",
      "type": "line",
      "stack": "总量",
      "areaStyle": {},
      "emphasis": {
        "focus": "series"
      },
      "data": [
        100, 102, 101, 105, 104, 108, 107, 112, 110, 115, 114, 120
      ]
    }
  ]
}
`);
const chartOption = ref({
	title: {
		text: '市场份额分布',
		left: 'center'
	},
	tooltip: {
		trigger: 'item',
		formatter: '{a} <br/>{b}: {c} ({d}%)'
	},
	legend: {
		orient: 'vertical',
		left: 'left',
		data: ['产品A', '产品B', '产品C', '产品D', '产品E']
	},
	series: [
		{
			name: '市场份额',
			type: 'pie',
			radius: '50%',
			center: ['50%', '60%'],
			data: [
				{ value: 335, name: '产品A' },
				{ value: 310, name: '产品B' },
				{ value: 234, name: '产品C' },
				{ value: 135, name: '产品D' },
				{ value: 1548, name: '产品E' }
			],
			emphasis: {
				itemStyle: {
					shadowBlur: 10,
					shadowOffsetX: 0,
					shadowColor: 'rgba(0, 0, 0, 0.5)'
				}
			}
		}
	]
});

const handleJson = (content: string) => {
	console.log(json5ToJsObject(content));
	return json5ToJsObject(content);
};
onMounted(() => {
	if (chartRef.value) {
		// 确保图表已经渲染
		chartRef.value.chart?.on('rendered', () => {
			const dataUrl = chartRef.value?.getDataURL({
				type: 'png',
				pixelRatio: 2,
				backgroundColor: '#fff'
			});
			if (dataUrl) {
				imageHtml.value = `<img src="${dataUrl}" alt="ECharts Image" />`;
			}
		});
	}
});
</script>
<style>
.box {
	width: 70%;
}
</style>
