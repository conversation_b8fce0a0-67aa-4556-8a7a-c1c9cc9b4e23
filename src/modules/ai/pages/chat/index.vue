<template>
	<div class="chat-box">
		<el-row :gutter="20" style="height: 100%">
			<el-col :span="16">
				<t-chat :data="chatList" :input-placeholder="inputPlaceholder" @send="handleSend">
					<template #content="{ item }">
						<t-chat-reasoning
							v-if="item.reasoning?.length > 0"
							expand-icon-placement="right"
						>
							<template #header>
								<x-chat-loading
									v-if="isStreamLoad && item.content.length === 0"
									text="思考中..."
								/>
								<div v-else style="display: flex; align-items: center">
									<CheckCircleIcon
										style="
											color: var(--td-success-color-5);
											font-size: 20px;
											margin-right: 8px;
										"
									/>
									<span>已深度思考</span>
								</div>
							</template>
							<x-chat-content
								v-if="item.reasoning.length > 0"
								:content="item.reasoning"
								:isStreamLoad="isStreamLoad"
							/>
						</t-chat-reasoning>

						<vue-markdown :markdown="item.content">
							<template #code="{ children, ...props }">
								<template v-if="props.language === 'echarts'">
									<t-chat-loading animation="moving" />
								</template>
								<code v-else>
									<component :is="children" />
								</code>
							</template>
						</vue-markdown>
					</template>
					<template #actions="{ item }">
						<t-chat-action
							:content="item.content"
							:operation-btn="['good', 'bad', 'replay', 'copy']"
							@operation="handleOperation"
						/>
					</template>

					<template #footer>
						<t-chat-input
							:stop-disabled="isStreamLoad"
							@send="inputEnter"
							@stop="onStop"
						>
						</t-chat-input>
					</template>
				</t-chat>
			</el-col>
			<el-col :span="8">
				<el-segmented v-model="segmentedValue" :options="options" size="large" />
				<v-chart
					v-if="segmentedValue == '图表'"
					:option="echartsCode"
					autoresize
					style="height: 500px; min-width: 375px; width: 100%; padding: 10px"
				/>
				<code-editor
					v-else-if="segmentedValue == '代码'"
					:code="echartsCode"
					lang="json"
					style="height: 500px; width: 100%"
				/>
			</el-col>
		</el-row>
	</div>
</template>

<script setup lang="jsx">
import { ref, computed } from 'vue';
import { MastraClient } from '@mastra/client-js';
import XChatContent from '../../components/x-chat-content';
import { VueMarkdown } from '@crazydos/vue-markdown';
import { json5ToJsObject } from '/@/utils/json5-to-js-object';
import { useChatStore } from '../../store/chat';
import CodeEditor from '../../components/code-editor.vue';
// 使用聊天 store
const chatStore = useChatStore();

// 初始化 MastraClient
const client = new MastraClient({
	baseUrl: '/aiApi'
});
const mastraAiAgent = client.getAgent('EChartsAgent');
const loading = ref(false);
const isStreamLoad = ref(false);
const echartsCode = ref('');
// 使用 store 中的消息列表
const chatList = computed(() => chatStore.messages);
const segmentedValue = ref('图表');
const options = ['图表', '代码'];
const chatRef = ref(null);
const isShowToBottom = ref(false);

// 清空消息
const clearConfirm = function () {
	chatStore.clearMessages();
};

const handleJson = content => {
	try {
		return json5ToJsObject(content);
	} catch (error) {
		return false;
	}
};

const handleOperation = function (type, options) {
	console.log('handleOperation', type, options);
};

const onStop = function () {
	loading.value = false;
	isStreamLoad.value = false;
};

const inputEnter = async inputValue => {
	if (isStreamLoad.value || !inputValue) return;

	// 添加用户消息到 store
	chatStore.addMessage({
		avatar: 'https://tdesign.gtimg.com/site/avatar.jpg',
		name: '自己',
		datetime: new Date().toLocaleTimeString(),
		content: inputValue,
		role: 'user'
	});

	// 添加助手占位消息
	chatStore.addMessage({
		avatar: 'https://tdesign.gtimg.com/site/chat-avatar.png',
		name: 'TDesignAI',
		datetime: new Date().toLocaleTimeString(),
		content: '',
		reasoning: '',
		role: 'assistant'
	});

	await handleData();
};

const handleData = async () => {
	loading.value = true;
	isStreamLoad.value = true;

	try {
		// 获取完整的对话历史
		const apiMessages = chatStore.getApiMessages();

		const response = await mastraAiAgent.stream({
			messages: apiMessages // 现在包含完整历史记录
		});

		response.processDataStream({
			onTextPart: text => {
				chatStore.updateLastMessage({
					content: (chatStore.messages[0].content || '') + text
				});
			},
			onFinishStepPart: file => {
				console.log('结束', file);
				isStreamLoad.value = false;
			},
			onDataPart: data => {
				console.log(data);
			},
			onErrorPart: error => {
				console.error(error);
			}
		});
	} catch (error) {
		console.error('Mastra AI Stream Error:', error);
		chatStore.updateLastMessage({
			role: 'assistant',
			content: '抱歉，AI助手出现错误，请稍后再试。',
			reasoning: '抱歉，AI助手出现错误，请稍后再试。'
		});
	} finally {
		chatStore.updateLastMessage({ duration: 20 });
		isStreamLoad.value = false;
		loading.value = false;
	}
};
</script>
<style lang="less">
/* 应用滚动条样式 */
::-webkit-scrollbar-thumb {
	background-color: var(--td-scrollbar-color);
}
::-webkit-scrollbar-thumb:horizontal:hover {
	background-color: var(--td-scrollbar-hover-color);
}
::-webkit-scrollbar-track {
	background-color: var(--td-scroll-track-color);
}
.chat-box {
	position: relative;
	height: 100vh;
	padding: 20px;
	background-color: #d9e1ff;
	.bottomBtn {
		position: absolute;
		left: 50%;
		margin-left: -20px;
		bottom: 210px;
		padding: 0;
		border: 0;
		width: 40px;
		height: 40px;
		border-radius: 50%;
		box-shadow:
			0px 8px 10px -5px rgba(0, 0, 0, 0.08),
			0px 16px 24px 2px rgba(0, 0, 0, 0.04),
			0px 6px 30px 5px rgba(0, 0, 0, 0.05);
	}
	.to-bottom {
		width: 40px;
		height: 40px;
		border: 1px solid #dcdcdc;
		box-sizing: border-box;
		background: var(--td-bg-color-container);
		border-radius: 50%;
		font-size: 24px;
		line-height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		.t-icon {
			font-size: 24px;
		}
	}
}

.model-select {
	display: flex;
	align-items: center;
	.t-select {
		width: 112px;
		height: 32px;
		margin-right: 8px;
		.t-input {
			border-radius: 32px;
			padding: 0 15px;
		}
	}
	.check-box {
		width: 112px;
		height: 32px;
		border-radius: 32px;
		border: 0;
		background: #e7e7e7;
		color: rgba(0, 0, 0, 0.9);
		box-sizing: border-box;
		flex: 0 0 auto;
		.t-button__text {
			display: flex;
			align-items: center;
			justify-content: center;
			span {
				margin-left: 4px;
			}
		}
	}
	.check-box.is-active {
		border: 1px solid #d9e1ff;
		background: #f2f3ff;
		color: var(--td-brand-color);
	}
}
:deep(.el-col) {
	background-color: white !important;
}
</style>
