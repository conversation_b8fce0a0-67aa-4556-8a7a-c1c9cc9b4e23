import { loadModule } from 'vue3-sfc-loader';
import * as Vue from 'vue';

const options = {
	moduleCache: {
		vue: Vue
	},
	async getFile(url: string) {
		return Promise.reject(`Cannot load file: ${url}`);
	},
	addStyle(css: string) {
		const style = document.createElement('style');
		style.textContent = css;
		document.head.appendChild(style);
	},
	log(type: string, ...args: any[]) {
		console[type](...args);
	}
};

export async function sfcPreview(code: string) {
	try {
		const component = await loadModule('preview.vue', {
			...options,
			async getFile() {
				return code;
			}
		});
		return component;
	} catch (error) {
		console.error('SFC Preview Error:', error);
		return null;
	}
}
