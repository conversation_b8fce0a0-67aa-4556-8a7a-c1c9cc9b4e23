<template>
	<div
		class="component-wrapper"
		:class="{
			'is-selected': isSelected,
			'is-hovered': isHovered,
			'is-preview': isPreview
		}"
		@click.stop="handleClick"
		@mouseenter="handleMouseEnter"
		@mouseleave="handleMouseLeave"
		@drop="handleDrop"
		@dragover="handleDragOver"
	>
		<!-- 组件选择框 -->
		<div
			v-if="!isPreview && (isSelected || isHovered)"
			:key="`selection-${component.id}-${isSelected ? 'selected' : 'hovered'}`"
			class="selection-box"
		>
			<div class="selection-label">{{ component.label }}</div>
			<div v-if="isSelected" class="selection-actions">
				<el-button
					size="small"
					type="info"
					@click.stop="handleMoveUp"
					:disabled="!canMoveUp"
				>
					上移
				</el-button>
				<el-button
					size="small"
					type="info"
					@click.stop="handleMoveDown"
					:disabled="!canMoveDown"
				>
					下移
				</el-button>
				<el-button size="small" type="primary" @click.stop="handleCopy"> 复制 </el-button>
				<el-button size="small" type="danger" @click.stop="handleDelete"> 删除 </el-button>
			</div>
		</div>

		<!-- CRUD组件渲染 -->
		<crud-renderer v-if="isCrudComponent()" :component="component" class="rendered-component">
			<!-- 渲染子组件 -->
			<template v-if="component.children && component.children.length > 0">
				<component-renderer
					v-for="child in component.children"
					:key="child.id"
					:component="child"
					:is-preview="isPreview"
					@select="$emit('select', $event)"
					@delete="$emit('delete', $event)"
				/>
			</template>

			<!-- 空容器提示 -->
			<div v-else-if="isContainerComponent() && !isPreview" class="empty-container">
				拖拽组件到此处
			</div>
		</crud-renderer>

		<!-- 普通组件渲染 -->
		<component
			v-else
			:is="getComponentTag()"
			v-bind="getComponentProps()"
			:style="getComponentStyle()"
			class="rendered-component"
		>
			<!-- 渲染子组件 -->
			<template v-if="component.children && component.children.length > 0">
				<component-renderer
					v-for="child in component.children"
					:key="child.id"
					:component="child"
					:is-preview="isPreview"
					@select="$emit('select', $event)"
					@delete="$emit('delete', $event)"
				/>
			</template>

			<!-- 文本内容 -->
			<template v-else-if="component.componentId === 'text'">
				{{ component.props.content }}
			</template>

			<!-- 按钮内容 -->
			<template v-else-if="component.componentId === 'button'">
				{{ component.props.text }}
			</template>

			<!-- 空容器提示 -->
			<div v-else-if="isContainerComponent() && !isPreview" class="empty-container">
				拖拽组件到此处
			</div>
		</component>

		<!-- 拖拽指示器 -->
		<div v-if="dragIndicator.show" class="drop-indicator" :style="dragIndicator.style" />
	</div>
</template>

<script setup lang="ts">
import { computed, reactive, nextTick, watch } from 'vue';
import { useBuilderStore } from '../../store/builder';
import type { CrudComponentInstance } from '../../types';
import CrudRenderer from './crud-renderer.vue';

interface Props {
	component: CrudComponentInstance;
	isPreview?: boolean;
}

interface Emits {
	(e: 'select', component: CrudComponentInstance): void;
	(e: 'delete', component: CrudComponentInstance): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const builderStore = useBuilderStore();

// 拖拽指示器
const dragIndicator = reactive({
	show: false,
	style: {}
});

// 计算属性
const isSelected = computed(() => {
	const selected = builderStore.selectedComponent?.id === props.component.id;
	return selected;
});

const isHovered = computed(
	() => builderStore.editorState.hoveredComponent?.id === props.component.id
);

// 判断是否可以上移/下移
const canMoveUp = computed(() => {
	if (!props.component.parentId) {
		// 根级组件
		const index = builderStore.currentPage.components.findIndex(
			c => c.id === props.component.id
		);
		return index > 0;
	} else {
		// 子组件
		const parent = builderStore.findComponentById(props.component.parentId);
		if (parent && parent.children) {
			const index = parent.children.findIndex(c => c.id === props.component.id);
			return index > 0;
		}
	}
	return false;
});

const canMoveDown = computed(() => {
	if (!props.component.parentId) {
		// 根级组件
		const components = builderStore.currentPage.components;
		const index = components.findIndex(c => c.id === props.component.id);
		return index >= 0 && index < components.length - 1;
	} else {
		// 子组件
		const parent = builderStore.findComponentById(props.component.parentId);
		if (parent && parent.children) {
			const index = parent.children.findIndex(c => c.id === props.component.id);
			return index >= 0 && index < parent.children.length - 1;
		}
	}
	return false;
});

// 调试选择状态变化
watch(
	() => builderStore.selectedComponent,
	(newSelected, oldSelected) => {
		if (newSelected?.id === props.component.id || oldSelected?.id === props.component.id) {
			console.log(
				`Component ${props.component.label} (${props.component.id}) selection changed:`,
				{
					isSelected: newSelected?.id === props.component.id,
					selectedComponent: newSelected?.id,
					componentId: props.component.id
				}
			);
		}
	},
	{ immediate: true }
);

// 获取组件标签
const getComponentTag = () => {
	const componentMap: Record<string, string> = {
		// CRUD 组件
		'cl-crud': 'cl-crud',
		'cl-table': 'cl-table',
		'cl-upsert': 'cl-upsert',
		'cl-search': 'cl-search',
		'cl-add-btn': 'cl-add-btn',
		'cl-refresh-btn': 'cl-refresh-btn',
		'cl-multi-delete-btn': 'cl-multi-delete-btn',
		'cl-row': 'cl-row',
		'cl-flex1': 'cl-flex1',
		'cl-pagination': 'cl-pagination',
		// 基础组件
		container: 'div',
		row: 'el-row',
		col: 'el-col',
		input: 'el-input',
		button: 'el-button',
		text: 'span'
	};

	return componentMap[props.component.componentId] || 'div';
};

// 获取组件属性
const getComponentProps = () => {
	const props_data = { ...props.component.props };

	// 特殊处理某些组件的属性
	if (props.component.componentId === 'table') {
		// 表格组件需要特殊处理
		return {
			data: props_data.data || [],
			border: props_data.border,
			stripe: props_data.stripe,
			size: props_data.size
		};
	}

	// 移除样式相关的属性，这些会在 style 中处理
	const styleProps = [
		'padding',
		'margin',
		'backgroundColor',
		'borderRadius',
		'fontSize',
		'color',
		'textAlign'
	];
	styleProps.forEach(prop => {
		delete props_data[prop];
	});

	return props_data;
};

// 获取组件样式
const getComponentStyle = () => {
	const style: Record<string, any> = { ...props.component.style };

	// 从 props 中提取样式属性
	const styleProps = [
		'padding',
		'margin',
		'backgroundColor',
		'borderRadius',
		'fontSize',
		'color',
		'textAlign'
	];
	styleProps.forEach(prop => {
		if (props.component.props[prop]) {
			style[prop] = props.component.props[prop];
		}
	});

	// 容器组件默认样式
	if (isContainerComponent()) {
		style.minHeight = style.minHeight || '50px';
		style.position = 'relative';
	}

	return style;
};

// 判断是否为CRUD组件
const isCrudComponent = () => {
	return props.component.componentId.startsWith('cl-');
};

// 判断是否为容器组件
const isContainerComponent = () => {
	return ['cl-crud', 'cl-row', 'container', 'row', 'col'].includes(props.component.componentId);
};

// 事件处理
const handleClick = async () => {
	if (!props.isPreview) {
		// 直接调用 store 方法确保立即更新
		builderStore.selectComponent(props.component);
		// 等待 DOM 更新
		await nextTick();
		// 同时发出事件保持兼容性
		emit('select', props.component);
	}
};

const handleMouseEnter = () => {
	if (!props.isPreview) {
		builderStore.hoverComponent(props.component);
	}
};

const handleMouseLeave = () => {
	if (!props.isPreview) {
		builderStore.hoverComponent(undefined);
	}
};

const handleCopy = () => {
	const copied = builderStore.copyComponent(props.component.id);
	if (copied) {
		builderStore.pasteComponent(copied, props.component.parentId);
	}
};

const handleDelete = () => {
	emit('delete', props.component);
};

const handleMoveUp = () => {
	builderStore.moveComponentUp(props.component.id);
};

const handleMoveDown = () => {
	builderStore.moveComponentDown(props.component.id);
};

// 拖拽处理
const handleDrop = (event: DragEvent) => {
	if (!isContainerComponent() || props.isPreview) return;

	event.preventDefault();
	event.stopPropagation();
	dragIndicator.show = false;

	try {
		const data = event.dataTransfer?.getData('application/json');
		if (data) {
			const dragData = JSON.parse(data);

			if (dragData.type === 'component') {
				builderStore.addComponent(dragData.data, props.component.id);
			}
		}
	} catch (error) {
		console.error('拖拽数据解析失败:', error);
	}
};

const handleDragOver = (event: DragEvent) => {
	if (!isContainerComponent() || props.isPreview) return;

	event.preventDefault();
	event.stopPropagation();
	event.dataTransfer!.dropEffect = 'copy';

	// 显示拖拽指示器
	const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
	dragIndicator.show = true;
	dragIndicator.style = {
		left: `${event.clientX - rect.left}px`,
		top: `${event.clientY - rect.top}px`
	};
};
</script>

<style scoped lang="scss">
.component-wrapper {
	position: relative;

	&:not(.is-preview) {
		&.is-selected {
			outline: 2px solid #409eff;
			outline-offset: 2px;
		}

		&.is-hovered:not(.is-selected) {
			outline: 1px dashed #409eff;
			outline-offset: 1px;
		}
	}

	.selection-box {
		position: absolute;
		top: -35px;
		left: 0;
		z-index: 100;
		display: flex;
		align-items: center;
		gap: 8px;
		background: #409eff;
		color: white;
		padding: 4px 8px;
		border-radius: 4px;
		font-size: 12px;
		white-space: nowrap;
		max-width: calc(100% - 16px);
		overflow: hidden;

		.selection-label {
			font-weight: 500;
			flex-shrink: 0;
		}

		.selection-actions {
			display: flex;
			gap: 2px;
			flex-wrap: wrap;

			.el-button {
				padding: 1px 3px;
				min-height: auto;
				font-size: 11px;
				line-height: 1.2;
			}
		}
	}

	.rendered-component {
		width: 100%;

		.empty-container {
			min-height: 50px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: #909399;
			font-size: 12px;
			border: 1px dashed #e4e7ed;
			border-radius: 4px;
			background: #fafafa;
		}
	}

	.drop-indicator {
		position: absolute;
		width: 100%;
		height: 2px;
		background: #409eff;
		pointer-events: none;
		z-index: 99;
	}
}

// 表格组件特殊样式
:deep(.el-table) {
	.el-table__header-wrapper,
	.el-table__body-wrapper {
		.el-table__cell {
			padding: 8px;
			font-size: 12px;
		}
	}
}
</style>
