<template>
	<div class="crud-renderer">
		<!-- CRUD容器 -->
		<div v-if="component.componentId === 'cl-crud'" class="crud-container" disabled>
			<repl-preview :sfc="generatedVueCode" :key="generatedVueCode"></repl-preview>
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import type { CrudComponentInstance, TableColumn, FormItem, CrudPageConfig } from '../../types';
import { CrudCodeGenerator } from '../../utils/crud-code-generator';
import { useBuilderStore } from '../../store/builder';
import ReplPreview from '../repl-preview.vue';
import dayjs from 'dayjs';

interface Props {
	component: CrudComponentInstance;
}
const builderStore = useBuilderStore();

const props = defineProps<Props>();

// 生成的代码
const generatedVueCode = computed(() => {
	return generateVueCode(builderStore.currentPage);
});
const comKey = ref(dayjs.unix(dayjs().unix()));
// 生成 Vue 代码
const generateVueCode = (page: CrudPageConfig): string => {
	const generator = new CrudCodeGenerator(page);
	return generator.generateVueCode();
};
</script>

<style scoped lang="scss">
.crud-renderer {
	width: 100%;

	.crud-container {
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		background: #fff;

		.crud-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px 16px;
			background: #f8f9fa;
			border-bottom: 1px solid #e4e7ed;

			.crud-title {
				font-weight: 500;
				color: #303133;
			}

			.crud-service {
				font-size: 12px;
				color: #909399;
			}
		}

		.crud-content {
			padding: 16px;
		}
	}

	.table-container {
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		background: #fff;

		.table-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px 16px;
			background: #f8f9fa;
			border-bottom: 1px solid #e4e7ed;

			.table-title {
				font-weight: 500;
				color: #303133;
			}

			.table-info {
				font-size: 12px;
				color: #909399;
			}
		}

		.mock-table {
			.table {
				width: 100%;
				border-collapse: collapse;

				th,
				td {
					padding: 8px 12px;
					border-bottom: 1px solid #ebeef5;
					text-align: left;

					.column-header {
						font-weight: 500;
						color: #909399;
					}

					.cell-content {
						display: flex;
						align-items: center;
						gap: 8px;
					}
				}

				th {
					background: #fafafa;
				}

				.mock-row:hover {
					background: #f5f7fa;
				}
			}
		}
	}

	.upsert-container,
	.search-container {
		border: 1px solid #e4e7ed;
		border-radius: 4px;
		background: #fff;

		.upsert-header,
		.search-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 12px 16px;
			background: #f8f9fa;
			border-bottom: 1px solid #e4e7ed;

			.upsert-title,
			.search-title {
				font-weight: 500;
				color: #303133;
			}

			.upsert-info,
			.search-info {
				font-size: 12px;
				color: #909399;
			}
		}

		.mock-form,
		.mock-search {
			padding: 16px;
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
			gap: 16px;

			.form-item,
			.search-item {
				.form-label,
				.search-label {
					display: block;
					margin-bottom: 4px;
					font-size: 14px;
					color: #606266;

					.required {
						color: #f56c6c;
					}
				}

				.form-control,
				.search-control {
					width: 100%;
				}
			}
		}
	}

	.action-button {
		display: inline-block;
		margin-right: 8px;
	}

	.search-key {
		display: inline-block;
	}

	.filter-container {
		display: inline-block;
		margin-right: 8px;
	}

	.row-layout {
		width: 100%;
		min-height: 40px;
		border: 1px dashed #e4e7ed;
		border-radius: 4px;
		padding: 8px;
	}

	.flex-spacer {
		flex: 1;
		height: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 1px dashed #e4e7ed;
		border-radius: 4px;
		background: #fafafa;

		.flex-indicator {
			font-size: 12px;
			color: #909399;
		}
	}

	.pagination-container {
		display: flex;
		justify-content: center;
		padding: 16px;
	}

	.form-item-component {
		margin-bottom: 16px;

		.form-item-preview {
			border: 1px solid #e4e7ed;
			border-radius: 4px;
			padding: 16px;
			background: #fff;

			.form-label {
				display: block;
				margin-bottom: 8px;
				font-size: 14px;
				color: #606266;
				font-weight: 500;

				.required {
					color: #f56c6c;
					margin-left: 2px;
				}
			}

			.form-control {
				width: 100%;
			}
		}
	}

	.table-column-component {
		margin-bottom: 8px;

		.column-preview {
			border: 1px solid #e4e7ed;
			border-radius: 4px;
			background: #fff;
			overflow: hidden;

			.column-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 8px 12px;
				background: #f8f9fa;
				border-bottom: 1px solid #e4e7ed;

				.column-title {
					font-size: 14px;
					font-weight: 500;
					color: #303133;
				}

				.column-type {
					font-size: 12px;
					color: #909399;
					background: #e4e7ed;
					padding: 2px 6px;
					border-radius: 2px;
				}
			}

			.column-sample {
				padding: 8px 12px;
				font-size: 14px;
				color: #606266;
			}
		}
	}

	.default-component {
		.component-placeholder {
			display: flex;
			align-items: center;
			gap: 8px;
			padding: 12px 16px;
			border: 1px dashed #e4e7ed;
			border-radius: 4px;
			background: #fafafa;

			.component-icon {
				font-size: 16px;
			}

			.component-name {
				font-size: 14px;
				color: #606266;
			}
		}
	}
}
</style>
