<template>
	<div class="items-editor">
		<div class="editor-header">
			<span>表单项配置</span>
			<el-button size="small" type="primary" @click="addItem">添加表单项</el-button>
		</div>

		<div class="items-list">
			<div
				v-for="(item, index) in items"
				:key="index"
				class="item-card"
				:class="{ active: activeIndex === index }"
				@click="selectItem(index)"
			>
				<div class="item-header">
					<span class="item-title">{{ item.label || `表单项${index + 1}` }}</span>
					<div class="item-actions">
						<el-button
							size="small"
							text
							@click.stop="moveItem(index, -1)"
							:disabled="index === 0"
						>
							↑
						</el-button>
						<el-button
							size="small"
							text
							@click.stop="moveItem(index, 1)"
							:disabled="index === items.length - 1"
						>
							↓
						</el-button>
						<el-button size="small" text type="danger" @click.stop="removeItem(index)">
							删除
						</el-button>
					</div>
				</div>

				<div v-if="activeIndex === index" class="item-config">
					<!-- 基本配置 -->
					<div class="config-group">
						<div class="group-title">基本配置</div>

						<div class="config-item">
							<label>标签</label>
							<el-input v-model="item.label" size="small" placeholder="表单项标签" />
						</div>

						<div class="config-item">
							<label>字段名</label>
							<el-input v-model="item.prop" size="small" placeholder="数据字段名" />
						</div>

						<div class="config-item">
							<label>是否必填</label>
							<el-switch v-model="item.required" />
						</div>

						<div class="config-item">
							<label>分组</label>
							<el-input v-model="item.group" size="small" placeholder="表单分组" />
						</div>

						<div class="config-item">
							<label>栅格占位</label>
							<el-input-number
								v-model="item.span"
								size="small"
								style="width: 100%"
								:min="1"
								:max="24"
							/>
						</div>
					</div>

					<!-- 组件配置 -->
					<div class="config-group">
						<div class="group-title">组件配置</div>

						<div class="config-item">
							<label>组件类型</label>
							<el-select
								v-model="item.component.name"
								size="small"
								style="width: 100%"
								@change="handleComponentChange(index)"
							>
								<el-option label="输入框" value="el-input" />
								<el-option label="数字输入框" value="el-input-number" />
								<el-option label="文本域" value="el-input" />
								<el-option label="选择器" value="el-select" />
								<el-option label="级联选择器" value="el-cascader" />
								<el-option label="日期选择器" value="el-date-picker" />
								<el-option label="时间选择器" value="el-time-picker" />
								<el-option label="开关" value="el-switch" />
								<el-option label="单选框组" value="el-radio-group" />
								<el-option label="多选框组" value="el-checkbox-group" />
								<el-option label="上传组件" value="cl-upload" />
								<el-option label="用户选择" value="cl-user-select" />
								<el-option label="树形选择" value="el-tree-select" />
							</el-select>
						</div>

						<!-- 输入框配置 -->
						<template v-if="item.component.name === 'el-input'">
							<div class="config-item">
								<label>输入类型</label>
								<el-select
									v-model="item.component.props.type"
									size="small"
									style="width: 100%"
								>
									<el-option label="文本" value="text" />
									<el-option label="密码" value="password" />
									<el-option label="文本域" value="textarea" />
								</el-select>
							</div>
							<div class="config-item">
								<label>占位符</label>
								<el-input v-model="item.component.props.placeholder" size="small" />
							</div>
							<div class="config-item">
								<label>可清空</label>
								<el-switch v-model="item.component.props.clearable" />
							</div>
						</template>

						<!-- 数字输入框配置 -->
						<template v-if="item.component.name === 'el-input-number'">
							<div class="config-item">
								<label>最小值</label>
								<el-input-number
									v-model="item.component.props.min"
									size="small"
									style="width: 100%"
								/>
							</div>
							<div class="config-item">
								<label>最大值</label>
								<el-input-number
									v-model="item.component.props.max"
									size="small"
									style="width: 100%"
								/>
							</div>
							<div class="config-item">
								<label>步长</label>
								<el-input-number
									v-model="item.component.props.step"
									size="small"
									style="width: 100%"
								/>
							</div>
						</template>

						<!-- 选择器配置 -->
						<template v-if="item.component.name === 'el-select'">
							<div class="config-item">
								<label>占位符</label>
								<el-input v-model="item.component.props.placeholder" size="small" />
							</div>
							<div class="config-item">
								<label>可清空</label>
								<el-switch v-model="item.component.props.clearable" />
							</div>
							<div class="config-item">
								<label>多选</label>
								<el-switch v-model="item.component.props.multiple" />
							</div>
							<div class="config-item">
								<label>选项配置</label>
								<el-button
									size="small"
									style="width: 100%"
									@click="editOptions(index)"
								>
									编辑选项
								</el-button>
							</div>
						</template>

						<!-- 日期选择器配置 -->
						<template v-if="item.component.name === 'el-date-picker'">
							<div class="config-item">
								<label>选择类型</label>
								<el-select
									v-model="item.component.props.type"
									size="small"
									style="width: 100%"
								>
									<el-option label="日期" value="date" />
									<el-option label="日期时间" value="datetime" />
									<el-option label="日期范围" value="daterange" />
									<el-option label="日期时间范围" value="datetimerange" />
								</el-select>
							</div>
							<div class="config-item">
								<label>值格式</label>
								<el-input
									v-model="item.component.props.valueFormat"
									size="small"
									placeholder="YYYY-MM-DD"
								/>
							</div>
						</template>

						<!-- 上传组件配置 -->
						<template v-if="item.component.name === 'cl-upload'">
							<div class="config-item">
								<label>上传类型</label>
								<el-select
									v-model="item.component.props.type"
									size="small"
									style="width: 100%"
								>
									<el-option label="图片" value="image" />
									<el-option label="文件" value="file" />
								</el-select>
							</div>
							<div class="config-item">
								<label>多选</label>
								<el-switch v-model="item.component.props.multiple" />
							</div>
						</template>
					</div>
				</div>
			</div>
		</div>

		<!-- 选项编辑对话框 -->
		<el-dialog v-model="optionsDialog.visible" title="编辑选项" width="500px">
			<div class="options-editor">
				<div
					v-for="(option, index) in optionsDialog.options"
					:key="index"
					class="option-item"
				>
					<el-input
						v-model="option.label"
						placeholder="选项标签"
						size="small"
						style="margin-right: 8px"
					/>
					<el-input
						v-model="option.value"
						placeholder="选项值"
						size="small"
						style="margin-right: 8px"
					/>
					<el-button size="small" type="danger" @click="removeOption(index)"
						>删除</el-button
					>
				</div>
				<el-button size="small" type="primary" @click="addOption">添加选项</el-button>
			</div>
			<template #footer>
				<el-button @click="optionsDialog.visible = false">取消</el-button>
				<el-button type="primary" @click="saveOptions">确定</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, watch, reactive } from 'vue';
import type { FormItem } from '../../../types';

const props = defineProps<{
	modelValue: FormItem[];
}>();

const emit = defineEmits<{
	'update:modelValue': [value: FormItem[]];
}>();

// 标准化表单项数据结构
const normalizeFormItem = (item: any): FormItem => {
	return {
		label: item.label || '表单项',
		prop: item.prop || 'field',
		required: item.required || false,
		group: item.group || '',
		span: item.span || 24,
		component: {
			name: item.component?.name || 'el-input',
			props: item.component?.props || {},
			options: item.component?.options || []
		}
	};
};

const items = ref<FormItem[]>(props.modelValue.map(item => normalizeFormItem(item)));
const activeIndex = ref(0);

// 选项编辑对话框
const optionsDialog = reactive({
	visible: false,
	itemIndex: -1,
	options: [] as Array<{ label: string; value: any }>
});

// 监听外部变化
watch(
	() => props.modelValue,
	newValue => {
		items.value = newValue.map(item => normalizeFormItem(item));
	},
	{ deep: true }
);

// 监听内部变化
watch(
	items,
	newValue => {
		emit('update:modelValue', newValue);
	},
	{ deep: true }
);

// 选择表单项
const selectItem = (index: number) => {
	activeIndex.value = index;
};

// 添加表单项
const addItem = () => {
	const newItem: FormItem = {
		label: '新表单项',
		prop: 'newField',
		component: {
			name: 'el-input',
			props: {}
		}
	};
	items.value.push(newItem);
	activeIndex.value = items.value.length - 1;
};

// 删除表单项
const removeItem = (index: number) => {
	items.value.splice(index, 1);
	if (activeIndex.value >= items.value.length) {
		activeIndex.value = Math.max(0, items.value.length - 1);
	}
};

// 移动表单项
const moveItem = (index: number, direction: number) => {
	const newIndex = index + direction;
	if (newIndex >= 0 && newIndex < items.value.length) {
		const item = items.value.splice(index, 1)[0];
		items.value.splice(newIndex, 0, item);
		activeIndex.value = newIndex;
	}
};

// 组件类型变化
const handleComponentChange = (index: number) => {
	const item = items.value[index];
	// 重置组件属性
	item.component.props = {};

	// 根据组件类型设置默认属性
	switch (item.component.name) {
		case 'el-input':
			item.component.props = { clearable: true };
			break;
		case 'el-select':
			item.component.props = { clearable: true, options: [] };
			break;
		case 'el-date-picker':
			item.component.props = { type: 'date', valueFormat: 'YYYY-MM-DD' };
			break;
		case 'cl-upload':
			item.component.props = { type: 'image' };
			break;
	}
};

// 编辑选项
const editOptions = (index: number) => {
	optionsDialog.itemIndex = index;
	optionsDialog.options = [...(items.value[index].component.options || [])];
	optionsDialog.visible = true;
};

// 添加选项
const addOption = () => {
	optionsDialog.options.push({ label: '', value: '' });
};

// 删除选项
const removeOption = (index: number) => {
	optionsDialog.options.splice(index, 1);
};

// 保存选项
const saveOptions = () => {
	if (optionsDialog.itemIndex >= 0) {
		items.value[optionsDialog.itemIndex].component.options = [...optionsDialog.options];
	}
	optionsDialog.visible = false;
};
</script>

<style scoped lang="scss">
.items-editor {
	.editor-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 16px;
		padding-bottom: 8px;
		border-bottom: 1px solid #e4e7ed;

		span {
			font-weight: 500;
			color: #303133;
		}
	}

	.items-list {
		.item-card {
			border: 1px solid #e4e7ed;
			border-radius: 4px;
			margin-bottom: 8px;
			overflow: hidden;

			&.active {
				border-color: #409eff;
			}

			.item-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 8px 12px;
				background: #f8f9fa;
				cursor: pointer;

				.item-title {
					font-size: 14px;
					color: #303133;
				}

				.item-actions {
					display: flex;
					gap: 4px;
				}
			}

			.item-config {
				padding: 12px;
				background: #fff;

				.config-group {
					margin-bottom: 16px;

					&:last-child {
						margin-bottom: 0;
					}

					.group-title {
						font-size: 12px;
						font-weight: 500;
						color: #909399;
						margin-bottom: 8px;
						padding-bottom: 4px;
						border-bottom: 1px solid #f0f0f0;
					}

					.config-item {
						margin-bottom: 12px;

						&:last-child {
							margin-bottom: 0;
						}

						label {
							display: block;
							font-size: 12px;
							color: #606266;
							margin-bottom: 4px;
						}
					}
				}
			}
		}
	}

	.options-editor {
		.option-item {
			display: flex;
			align-items: center;
			margin-bottom: 8px;
		}
	}
}
</style>
