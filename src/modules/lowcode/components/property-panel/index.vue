<template>
	<div class="property-panel">
		<div class="panel-header">
			<h3>属性配置</h3>
		</div>

		<div class="panel-content">
			<template v-if="selectedComponent">
				<!-- 基本信息 -->
				<div class="property-section">
					<div class="section-title">基本信息</div>
					<div class="property-item">
						<label>组件ID</label>
						<el-input :model-value="selectedComponent.id" disabled size="small" />
					</div>
					<div class="property-item">
						<label>组件名称</label>
						<el-input
							v-model="selectedComponent.label"
							size="small"
							@input="handleLabelChange"
						/>
					</div>
				</div>

				<!-- 属性配置 -->
				<div class="property-section">
					<div class="section-title">属性配置</div>
					<div
						v-for="prop in componentConfig?.props || []"
						:key="prop.name"
						class="property-item"
					>
						<label :title="prop.description">
							{{ prop.label }}
							<span v-if="prop.required" class="required">*</span>
						</label>

						<!-- 字符串输入 -->
						<el-input
							v-if="prop.type === 'string'"
							:model-value="selectedComponent.props[prop.name]"
							:placeholder="prop.default"
							size="small"
							@input="handlePropChange(prop.name, $event)"
						/>

						<!-- 数字输入 -->
						<el-input-number
							v-else-if="prop.type === 'number'"
							:model-value="selectedComponent.props[prop.name]"
							:placeholder="prop.default"
							size="small"
							style="width: 100%"
							@change="handlePropChange(prop.name, $event)"
						/>

						<!-- 布尔值开关 -->
						<el-switch
							v-else-if="prop.type === 'boolean'"
							:model-value="selectedComponent.props[prop.name]"
							@change="handlePropChange(prop.name, $event)"
						/>

						<!-- 选择器 -->
						<el-select
							v-else-if="prop.type === 'select'"
							:model-value="selectedComponent.props[prop.name]"
							size="small"
							style="width: 100%"
							@change="handlePropChange(prop.name, $event)"
						>
							<el-option
								v-for="option in prop.options"
								:key="option.value"
								:label="option.label"
								:value="option.value"
							/>
						</el-select>

						<!-- 颜色选择器 -->
						<el-color-picker
							v-else-if="prop.type === 'color'"
							:model-value="selectedComponent.props[prop.name]"
							size="small"
							@change="handlePropChange(prop.name, $event)"
						/>

						<!-- 日期选择器 -->
						<el-date-picker
							v-else-if="prop.type === 'date'"
							:model-value="selectedComponent.props[prop.name]"
							type="date"
							size="small"
							style="width: 100%"
							@change="handlePropChange(prop.name, $event)"
						/>

						<!-- 列配置编辑器 -->
						<div v-else-if="prop.type === 'columns'">
							<el-button
								size="small"
								style="width: 100%"
								@click="openColumnsEditor(prop.name)"
							>
								编辑{{ prop.label }}
							</el-button>
						</div>

						<!-- 表单项编辑器 -->
						<div v-else-if="prop.type === 'items'">
							<el-button
								size="small"
								style="width: 100%"
								@click="openItemsEditor(prop.name)"
							>
								编辑{{ prop.label }}
							</el-button>
						</div>

						<!-- 服务选择器 -->
						<div v-else-if="prop.type === 'service'">
							<el-select
								:model-value="selectedComponent.props[prop.name]"
								size="small"
								style="width: 100%"
								@change="handlePropChange(prop.name, $event)"
							>
								<el-option label="测试服务" value="test" />
								<el-option label="用户服务" value="service.base.sys.user" />
								<el-option label="角色服务" value="service.base.sys.role" />
								<el-option label="部门服务" value="service.base.sys.dept" />
							</el-select>
						</div>

						<!-- 数组/对象编辑器 -->
						<div v-else-if="prop.type === 'array' || prop.type === 'object'">
							<el-button
								size="small"
								style="width: 100%"
								@click="openJsonEditor(prop.name)"
							>
								编辑{{ prop.label }}
							</el-button>
						</div>
					</div>
				</div>

				<!-- 样式配置 -->
				<div class="property-section">
					<div class="section-title">样式配置</div>

					<!-- 布局样式 -->
					<div class="style-group">
						<div class="group-title">布局</div>
						<div class="property-item">
							<label>宽度</label>
							<el-input
								:model-value="selectedComponent.style.width"
								placeholder="auto"
								size="small"
								@input="handleStyleChange('width', $event)"
							/>
						</div>
						<div class="property-item">
							<label>高度</label>
							<el-input
								:model-value="selectedComponent.style.height"
								placeholder="auto"
								size="small"
								@input="handleStyleChange('height', $event)"
							/>
						</div>
					</div>

					<!-- 间距样式 -->
					<div class="style-group">
						<div class="group-title">间距</div>
						<div class="property-item">
							<label>内边距</label>
							<el-input
								:model-value="selectedComponent.style.padding"
								placeholder="0"
								size="small"
								@input="handleStyleChange('padding', $event)"
							/>
						</div>
						<div class="property-item">
							<label>外边距</label>
							<el-input
								:model-value="selectedComponent.style.margin"
								placeholder="0"
								size="small"
								@input="handleStyleChange('margin', $event)"
							/>
						</div>
					</div>

					<!-- 背景样式 -->
					<div class="style-group">
						<div class="group-title">背景</div>
						<div class="property-item">
							<label>背景色</label>
							<el-color-picker
								:model-value="selectedComponent.style.backgroundColor"
								size="small"
								@change="handleStyleChange('backgroundColor', $event)"
							/>
						</div>
					</div>

					<!-- 边框样式 -->
					<div class="style-group">
						<div class="group-title">边框</div>
						<div class="property-item">
							<label>边框</label>
							<el-input
								:model-value="selectedComponent.style.border"
								placeholder="none"
								size="small"
								@input="handleStyleChange('border', $event)"
							/>
						</div>
						<div class="property-item">
							<label>圆角</label>
							<el-input
								:model-value="selectedComponent.style.borderRadius"
								placeholder="0"
								size="small"
								@input="handleStyleChange('borderRadius', $event)"
							/>
						</div>
					</div>
				</div>
			</template>

			<!-- 未选中组件时的提示 -->
			<div v-else class="empty-state">
				<div class="empty-icon">⚙️</div>
				<p>请选择一个组件来配置属性</p>
			</div>
		</div>

		<!-- 列配置编辑器对话框 -->
		<el-dialog
			v-model="columnsEditor.visible"
			:title="`编辑 ${columnsEditor.propName}`"
			width="800px"
		>
			<columns-editor v-model="columnsEditor.data" />
			<template #footer>
				<el-button @click="columnsEditor.visible = false">取消</el-button>
				<el-button type="primary" @click="saveColumnsData">确定</el-button>
			</template>
		</el-dialog>

		<!-- 表单项编辑器对话框 -->
		<el-dialog
			v-model="itemsEditor.visible"
			:title="`编辑 ${itemsEditor.propName}`"
			width="800px"
		>
			<items-editor v-model="itemsEditor.data" />
			<template #footer>
				<el-button @click="itemsEditor.visible = false">取消</el-button>
				<el-button type="primary" @click="saveItemsData">确定</el-button>
			</template>
		</el-dialog>

		<!-- JSON 编辑器对话框 -->
		<el-dialog
			v-model="jsonEditor.visible"
			:title="`编辑 ${jsonEditor.propName}`"
			width="600px"
		>
			<code-editor v-model="jsonEditor.content" language="json" />
			<template #footer>
				<el-button @click="jsonEditor.visible = false">取消</el-button>
				<el-button type="primary" @click="saveJsonData">确定</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { computed, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { useBuilderStore } from '../../store/builder';
import {
	CRUD_BUILTIN_COMPONENTS,
	FORM_ITEM_COMPONENTS,
	TABLE_COLUMN_COMPONENTS
} from '../../types';
import ColumnsEditor from './editors/columns-editor.vue';
import ItemsEditor from './editors/items-editor.vue';
import CodeEditor from '../code-editor/index.vue';

const builderStore = useBuilderStore();

// 列配置编辑器状态
const columnsEditor = reactive<{
	visible: boolean;
	propName: string;
	data: any[];
}>({
	visible: false,
	propName: '',
	data: []
});

// 表单项编辑器状态
const itemsEditor = reactive({
	visible: false,
	propName: '',
	data: [] as any[]
});

// JSON 编辑器状态
const jsonEditor = reactive({
	visible: false,
	propName: '',
	content: ''
});

// 计算属性
const selectedComponent = computed(() => builderStore.selectedComponent);

const componentConfig = computed(() => {
	if (!selectedComponent.value) return null;

	// 在所有组件库中查找
	const allComponents = [
		...CRUD_BUILTIN_COMPONENTS,
		...FORM_ITEM_COMPONENTS,
		...TABLE_COLUMN_COMPONENTS
	];

	return allComponents.find(c => c.id === selectedComponent.value!.componentId);
});

// 处理标签变更
const handleLabelChange = (value: string) => {
	if (selectedComponent.value) {
		selectedComponent.value.label = value;
		builderStore.saveToHistory();
	}
};

// 处理属性变更
const handlePropChange = (propName: string, value: any) => {
	if (selectedComponent.value) {
		builderStore.updateComponentProps(selectedComponent.value.id, {
			[propName]: value
		});
	}
};

// 处理样式变更
const handleStyleChange = (styleName: string, value: any) => {
	if (selectedComponent.value) {
		builderStore.updateComponentStyle(selectedComponent.value.id, {
			[styleName]: value
		});
	}
};

// 打开列配置编辑器
const openColumnsEditor = (propName: string) => {
	if (selectedComponent.value) {
		columnsEditor.propName = propName;
		columnsEditor.data = [...(selectedComponent.value.props[propName] || [])];
		columnsEditor.visible = true;
	}
};

// 保存列配置数据
const saveColumnsData = () => {
	handlePropChange(columnsEditor.propName, columnsEditor.data);
	columnsEditor.visible = false;
	ElMessage.success('列配置已保存');
};

// 打开表单项编辑器
const openItemsEditor = (propName: string) => {
	if (selectedComponent.value) {
		itemsEditor.propName = propName;
		itemsEditor.data = [...(selectedComponent.value.props[propName] || [])];
		itemsEditor.visible = true;
	}
};

// 保存表单项数据
const saveItemsData = () => {
	handlePropChange(itemsEditor.propName, itemsEditor.data);
	itemsEditor.visible = false;
	ElMessage.success('表单项配置已保存');
};

// 打开 JSON 编辑器
const openJsonEditor = (propName: string) => {
	if (selectedComponent.value) {
		jsonEditor.propName = propName;
		jsonEditor.content = JSON.stringify(selectedComponent.value.props[propName] || {}, null, 2);
		jsonEditor.visible = true;
	}
};

// 保存 JSON 数据
const saveJsonData = () => {
	try {
		const data = JSON.parse(jsonEditor.content);
		console.log('保存json数据', jsonEditor.propName, data);

		handlePropChange(jsonEditor.propName, data);
		jsonEditor.visible = false;
		ElMessage.success('数据已保存');
	} catch (error) {
		ElMessage.error('JSON 格式错误，请检查后重试');
	}
};
</script>

<style scoped lang="scss">
.property-panel {
	width: 320px;
	height: 100%;
	background: #fff;
	border-left: 1px solid #e4e7ed;
	display: flex;
	flex-direction: column;

	.panel-header {
		padding: 16px;
		border-bottom: 1px solid #e4e7ed;
		background: #f8f9fa;

		h3 {
			margin: 0;
			font-size: 16px;
			font-weight: 600;
			color: #303133;
		}
	}

	.panel-content {
		flex: 1;
		overflow-y: auto;
		padding: 16px;

		.property-section {
			margin-bottom: 24px;

			.section-title {
				font-size: 14px;
				font-weight: 600;
				color: #303133;
				margin-bottom: 12px;
				padding-bottom: 8px;
				border-bottom: 1px solid #f0f0f0;
			}

			.property-item {
				margin-bottom: 12px;

				label {
					display: block;
					font-size: 12px;
					color: #606266;
					margin-bottom: 4px;
					line-height: 1.4;

					.required {
						color: #f56c6c;
					}
				}
			}

			.style-group {
				margin-bottom: 16px;

				.group-title {
					font-size: 12px;
					font-weight: 500;
					color: #909399;
					margin-bottom: 8px;
				}
			}
		}

		.empty-state {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			height: 200px;
			color: #909399;

			.empty-icon {
				font-size: 48px;
				margin-bottom: 12px;
				opacity: 0.5;
			}

			p {
				font-size: 14px;
				margin: 0;
			}
		}
	}
}

// 滚动条样式
.panel-content::-webkit-scrollbar {
	width: 6px;
}

.panel-content::-webkit-scrollbar-track {
	background: #f1f1f1;
	border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
	background: #c1c1c1;
	border-radius: 3px;

	&:hover {
		background: #a8a8a8;
	}
}
</style>
