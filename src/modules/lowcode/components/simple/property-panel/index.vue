<template>
	<div class="simple-property-panel">
		<div v-if="selectedComponent">
			<h3>属性面板</h3>
			<div>ID: {{ selectedComponent.id }}</div>
			<div>类型: {{ selectedComponent.type }}</div>

			<!-- 通用属性编辑 -->
			<div v-if="selectedComponent.props.hasOwnProperty('text')">
				<label>文本:</label>
				<input
					:value="selectedComponent.props.text"
					@input="updateProp('text', ($event.target as HTMLInputElement).value)"
				/>
			</div>

			<div v-if="selectedComponent.props.hasOwnProperty('placeholder')">
				<label>占位符:</label>
				<input
					:value="selectedComponent.props.placeholder"
					@input="updateProp('placeholder', ($event.target as HTMLInputElement).value)"
				/>
			</div>

			<!-- 特定组件属性编辑 -->
			<div v-if="selectedComponent.type === 'button'">
				<label>按钮文本:</label>
				<input
					:value="selectedComponent.props.text"
					@input="updateProp('text', ($event.target as HTMLInputElement).value)"
				/>
			</div>
		</div>
		<div v-else>
			<p>请在画布中选择一个组件进行编辑。</p>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useSimpleBuilderStore } from '../../../store/simpleBuilder';
import { storeToRefs } from 'pinia';

const store = useSimpleBuilderStore();
const { selectedComponent } = storeToRefs(store);

const updateProp = (key: string, value: any) => {
	if (selectedComponent.value) {
		store.updateComponentProps({
			id: selectedComponent.value.id,
			props: { [key]: value }
		});
	}
};
</script>

<style scoped>
.simple-property-panel {
	padding: 10px;
	border-left: 1px solid #eee;
	background-color: #fff;
	width: 300px;
}
</style>
