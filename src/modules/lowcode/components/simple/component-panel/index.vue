<template>
	<div class="simple-component-panel">
		<div
			v-for="component in componentList"
			:key="component.type"
			class="component-item"
			draggable="true"
			@dragstart="onDragStart($event, component)"
		>
			{{ component.name }}
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

const componentList = ref([
	{ name: '文本', type: 'text' },
	{ name: '按钮', type: 'button' },
	{ name: '输入框', type: 'input' }
]);

const onDragStart = (event: DragEvent, component: { type: string }) => {
	if (event.dataTransfer) {
		event.dataTransfer.setData('componentType', component.type);
	}
};
</script>

<style scoped>
.simple-component-panel {
	padding: 10px;
	border-right: 1px solid #eee;
	background-color: #fff;
	width: 250px;
}

.component-item {
	padding: 8px 12px;
	border: 1px solid #ddd;
	border-radius: 4px;
	margin-bottom: 8px;
	cursor: grab;
	background-color: #f9f9f9;
	text-align: center;
}

.component-item:hover {
	background-color: #efefef;
	border-color: #ccc;
}
</style>
