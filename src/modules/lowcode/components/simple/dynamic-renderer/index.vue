<template>
	<component :is="dynamicComponent" />
</template>

<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue';
import { sfcPreview } from '../../../utils/sfc-preview';

const props = defineProps<{
	code: string;
}>();

const dynamicComponent = computed(() => {
	if (!props.code) {
		return null;
	}

	return defineAsyncComponent({
		loader: async () => {
			const component = await sfcPreview(props.code);
			if (!component) {
				throw new Error('Component compilation failed');
			}
			return component;
		},
		loadingComponent: {
			template: '<div>加载中...</div>'
		},
		errorComponent: {
			template: '<div>加载失败</div>'
		},
		delay: 300,
		onError: (error, retry, fail, attempts) => {
			console.error('组件加载错误:', error);
			if (attempts < 3) {
				console.warn(`尝试重新加载组件，次数: ${attempts}`);
				retry();
			} else {
				console.error('组件加载失败');
				fail();
			}
		}
	});
});
</script>
