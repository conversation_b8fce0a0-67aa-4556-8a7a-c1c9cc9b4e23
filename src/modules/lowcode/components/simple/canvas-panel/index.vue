<template>
	<div class="simple-canvas-panel" @dragover.prevent @drop="handleDrop">
		<div class="component-list">
			<!-- <h3>已添加组件</h3> -->
			<dynamic-renderer :code="generatedCode" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useSimpleBuilderStore } from '../../../store/simpleBuilder';
import { storeToRefs } from 'pinia';
import { CrudCodeGenerator } from '../../../utils/crud-code-generator';
import DynamicRenderer from '../dynamic-renderer/index.vue';

const store = useSimpleBuilderStore();
const { pageConfig, components, selectedComponentId } = storeToRefs(store);

const generatedCode = computed(() => {
	const generator = new CrudCodeGenerator(pageConfig.value);
	return generator.generateVueCode();
});

const handleDrop = (event: DragEvent) => {
	if (event.dataTransfer) {
		const componentType = event.dataTransfer.getData('componentType');
		if (componentType) {
			store.addComponent(componentType);
		}
	}
};
</script>

<style scoped>
.simple-canvas-panel {
	padding: 20px;
	background-color: #f5f5f5;
	border: 1px dashed #cccccc;
	min-height: 400px;
	flex: 1;
}

pre {
	background-color: #282c34;
	color: #abb2bf;
	padding: 1em;
	border-radius: 5px;
	font-family: 'Courier New', Courier, monospace;
	white-space: pre-wrap;
	word-wrap: break-word;
}

.component-list {
	margin-top: 20px;
}

.component-list ul {
	list-style: none;
	padding: 0;
}

.component-list li {
	padding: 5px;
	cursor: pointer;
	border: 1px solid #eee;
	margin-bottom: 5px;
}

.component-list li.selected {
	background-color: #dbeafe;
	border-color: #3b82f6;
}
</style>
