import ElementPlus from 'element-plus';
import * as Vue from 'vue';
import { loadModule } from 'vue3-sfc-loader';
import * as CoolCurd from '@cool-vue/crud';
import * as aliases1 from '/@/cool';
import * as dict from '/$/dict';

// 2. 别名映射配置
const aliases = {
	'/@/': '/src/',
	'/$': '/src/modules/',
	'/#': '/src/plugins/',
	'/~': '/packages/'
};

// 3. 解析路径（处理别名和扩展名）
function resolvePath(path) {
	// 处理别名
	for (const [alias, target] of Object.entries(aliases)) {
		if (path.startsWith(alias)) {
			path = path.replace(alias, target);
		}
	}

	// 添加默认扩展名
	if (!path.includes('.')) {
		return `${path}.js`;
	}
	return path;
}

export function sfcPreview(vueFileString: string) {
	return Vue.defineAsyncComponent(async () =>
		loadModule('./virtual.vue', {
			moduleCache: {
				vue: Vue,
				'element-plus': ElementPlus,
				'@cool-vue/crud': CoolCurd,
				'/@/cool': aliases1,
				'/$/dict': dict
			},
			getFile: () => vueFileString,
			addStyle: css => {
				const style = document.createElement('style');
				style.textContent = css;
				document.head.appendChild(style);
			},
			resolvedPath: resolvePath
		})
	);
}
function fileURLToPath(arg0: URL) {
	throw new Error('Function not implemented.');
}
