<template>
	<div class="preview-renderer">
		<div v-if="components.length === 0" class="empty-preview">
			<p>暂无组件</p>
		</div>
		<div v-else class="preview-container">
			<!-- 渲染CRUD组件 -->
			<template v-if="isCrudPage">
				<div class="crud-preview">
					<div class="crud-header">
						<el-button type="primary" @click="handleAdd">新增</el-button>
						<el-button @click="handleRefresh">刷新</el-button>
						<el-input v-model="searchKey" placeholder="搜索关键字" style="width: 200px;" clearable />
					</div>
					<el-table :data="mockTableData" style="width: 100%; margin-top: 16px;">
						<el-table-column type="selection" width="55" />
						<el-table-column prop="name" label="姓名" />
						<el-table-column prop="phone" label="手机号" />
						<el-table-column prop="createTime" label="创建时间" />
						<el-table-column label="操作" width="180">
							<template #default>
								<el-button size="small" @click="handleEdit">编辑</el-button>
								<el-button size="small" type="danger" @click="handleDelete">删除</el-button>
							</template>
						</el-table-column>
					</el-table>
					<div style="margin-top: 16px; text-align: right;">
						<el-pagination 
							:total="100" 
							:page-size="10" 
							layout="total, prev, pager, next"
							@current-change="handlePageChange"
						/>
					</div>
				</div>
			</template>

			<!-- 渲染标准组件 -->
			<template v-else>
				<el-form :model="formData" label-width="120px" class="preview-form">
					<template v-for="component in components" :key="component.id">
						<!-- 表单输入框 -->
						<el-form-item 
							v-if="component.componentId === 'form-item-input'"
							:label="component.props.label || '输入框'"
						>
							<el-input 
								v-model="formData[component.props.prop || 'input']"
								:placeholder="component.props.placeholder || '请输入'"
								clearable
							/>
						</el-form-item>

						<!-- 数字输入框 -->
						<el-form-item 
							v-else-if="component.componentId === 'form-item-number'"
							:label="component.props.label || '数字输入框'"
						>
							<el-input-number 
								v-model="formData[component.props.prop || 'number']"
								:placeholder="component.props.placeholder || '请输入数字'"
							/>
						</el-form-item>

						<!-- 选择器 -->
						<el-form-item 
							v-else-if="component.componentId === 'form-item-select'"
							:label="component.props.label || '选择器'"
						>
							<el-select 
								v-model="formData[component.props.prop || 'select']"
								:placeholder="component.props.placeholder || '请选择'"
								clearable
							>
								<el-option 
									v-for="option in component.props.options || defaultOptions"
									:key="option.value"
									:label="option.label"
									:value="option.value"
								/>
							</el-select>
						</el-form-item>

						<!-- 日期选择器 -->
						<el-form-item 
							v-else-if="component.componentId === 'form-item-date'"
							:label="component.props.label || '日期选择器'"
						>
							<el-date-picker 
								v-model="formData[component.props.prop || 'date']"
								type="date"
								:placeholder="component.props.placeholder || '请选择日期'"
								value-format="YYYY-MM-DD"
							/>
						</el-form-item>

						<!-- 文本域 -->
						<el-form-item 
							v-else-if="component.componentId === 'form-item-textarea'"
							:label="component.props.label || '文本域'"
						>
							<el-input 
								v-model="formData[component.props.prop || 'textarea']"
								type="textarea"
								:rows="3"
								:placeholder="component.props.placeholder || '请输入'"
							/>
						</el-form-item>

						<!-- 开关 -->
						<el-form-item 
							v-else-if="component.componentId === 'form-item-switch'"
							:label="component.props.label || '开关'"
						>
							<el-switch v-model="formData[component.props.prop || 'switch']" />
						</el-form-item>
					</template>

					<!-- 操作按钮 -->
					<el-form-item>
						<template v-for="component in buttonComponents" :key="component.id">
							<el-button 
								v-if="component.componentId === 'cl-add-btn'"
								type="primary" 
								@click="handleAdd"
							>
								新增
							</el-button>
							<el-button 
								v-else-if="component.componentId === 'cl-refresh-btn'"
								@click="handleRefresh"
							>
								刷新
							</el-button>
							<el-button 
								v-else-if="component.componentId === 'cl-multi-delete-btn'"
								type="danger" 
								@click="handleMultiDelete"
							>
								批量删除
							</el-button>
						</template>
					</el-form-item>
				</el-form>
			</template>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage } from 'element-plus';
import type { CrudComponentInstance } from '../types';

interface Props {
	components: CrudComponentInstance[];
}

const props = defineProps<Props>();

// 判断是否为CRUD页面
const isCrudPage = computed(() => {
	return props.components.some(component => 
		['cl-crud', 'cl-table', 'cl-upsert'].includes(component.componentId)
	);
});

// 获取按钮组件
const buttonComponents = computed(() => {
	return props.components.filter(component => 
		['cl-add-btn', 'cl-refresh-btn', 'cl-multi-delete-btn'].includes(component.componentId)
	);
});

// 表单数据
const formData = reactive<Record<string, any>>({});

// 初始化表单数据
props.components.forEach(component => {
	if (component.componentId.startsWith('form-item-')) {
		const prop = component.props.prop || 'field';
		formData[prop] = getDefaultValue(component.componentId);
	}
});

// 搜索关键字
const searchKey = ref('');

// 模拟表格数据
const mockTableData = ref([
	{ name: '张三', phone: '13800138000', createTime: '2024-01-01' },
	{ name: '李四', phone: '13800138001', createTime: '2024-01-02' },
	{ name: '王五', phone: '13800138002', createTime: '2024-01-03' },
	{ name: '赵六', phone: '13800138003', createTime: '2024-01-04' },
	{ name: '钱七', phone: '13800138004', createTime: '2024-01-05' }
]);

// 默认选项
const defaultOptions = [
	{ label: '选项1', value: '1' },
	{ label: '选项2', value: '2' },
	{ label: '选项3', value: '3' }
];

// 获取默认值
const getDefaultValue = (componentId: string) => {
	const defaultValues: Record<string, any> = {
		'form-item-input': '',
		'form-item-number': 0,
		'form-item-select': '',
		'form-item-date': '',
		'form-item-textarea': '',
		'form-item-switch': false
	};
	return defaultValues[componentId] || '';
};

// 事件处理
const handleAdd = () => {
	ElMessage.success('新增功能演示');
};

const handleRefresh = () => {
	ElMessage.success('刷新功能演示');
};

const handleMultiDelete = () => {
	ElMessage.warning('批量删除功能演示');
};

const handleEdit = () => {
	ElMessage.info('编辑功能演示');
};

const handleDelete = () => {
	ElMessage.warning('删除功能演示');
};

const handlePageChange = (page: number) => {
	ElMessage.info(`切换到第${page}页`);
};
</script>

<style scoped lang="scss">
.preview-renderer {
	padding: 20px;
}

.empty-preview {
	text-align: center;
	padding: 60px 20px;
	color: #909399;
	font-size: 16px;
}

.preview-container {
	.crud-preview {
		.crud-header {
			display: flex;
			gap: 12px;
			align-items: center;
			margin-bottom: 16px;
			flex-wrap: wrap;
		}
	}

	.preview-form {
		max-width: 600px;

		.el-form-item {
			margin-bottom: 20px;
		}

		.el-button {
			margin-right: 12px;
		}
	}
}
</style>
