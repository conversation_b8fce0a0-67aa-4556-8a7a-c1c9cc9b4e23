<template>
	<div class="component-library">
		<div class="library-header">
			<h2>组件库管理</h2>
			<el-button type="primary" :icon="Plus" @click="handleAddComponent">
				添加组件
			</el-button>
		</div>

		<div class="library-content">
			<!-- 组件分类标签 -->
			<div class="category-tabs">
				<el-tabs v-model="activeCategory" @tab-click="handleCategoryChange">
					<el-tab-pane
						v-for="category in categories"
						:key="category.key"
						:label="category.label"
						:name="category.key"
					>
						<!-- 组件网格 -->
						<div class="component-grid">
							<div
								v-for="component in getComponentsByCategory(category.key)"
								:key="component.id"
								class="component-card"
								@click="handleComponentClick(component)"
							>
								<div class="card-header">
									<div class="component-icon">
										<el-icon
											><component :is="component.icon || 'Grid'"
										/></el-icon>
									</div>
									<div class="component-actions">
										<el-button
											size="small"
											type="primary"
											:icon="Edit"
											@click.stop="handleEditComponent(component)"
										/>
										<el-button
											size="small"
											type="danger"
											:icon="Delete"
											@click.stop="handleDeleteComponent(component)"
										/>
									</div>
								</div>

								<div class="card-body">
									<h4>{{ component.label }}</h4>
									<p>{{ component.name }}</p>
									<div class="component-props">
										<el-tag
											v-for="prop in component.props.slice(0, 3)"
											:key="prop.name"
											size="small"
											type="info"
										>
											{{ prop.label }}
										</el-tag>
										<el-tag
											v-if="component.props.length > 3"
											size="small"
											type="info"
										>
											+{{ component.props.length - 3 }}
										</el-tag>
									</div>
								</div>
							</div>

							<!-- 添加组件卡片 -->
							<div class="component-card add-card" @click="handleAddComponent">
								<div class="add-content">
									<el-icon class="add-icon"><Plus /></el-icon>
									<span>添加组件</span>
								</div>
							</div>
						</div>
					</el-tab-pane>
				</el-tabs>
			</div>
		</div>

		<!-- 组件编辑对话框 -->
		<el-dialog
			v-model="componentDialog.visible"
			:title="componentDialog.title"
			width="800px"
			:close-on-click-modal="false"
		>
			<el-form
				ref="componentFormRef"
				:model="componentForm"
				:rules="componentFormRules"
				label-width="120px"
			>
				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="组件ID" prop="id">
							<el-input
								v-model="componentForm.id"
								placeholder="请输入组件ID"
								:disabled="componentDialog.mode === 'edit'"
							/>
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="组件名称" prop="name">
							<el-input v-model="componentForm.name" placeholder="请输入组件名称" />
						</el-form-item>
					</el-col>
				</el-row>

				<el-row :gutter="20">
					<el-col :span="12">
						<el-form-item label="显示标签" prop="label">
							<el-input v-model="componentForm.label" placeholder="请输入显示标签" />
						</el-form-item>
					</el-col>
					<el-col :span="12">
						<el-form-item label="组件分类" prop="category">
							<el-select v-model="componentForm.category" placeholder="请选择分类">
								<el-option
									v-for="category in categories"
									:key="category.key"
									:label="category.label"
									:value="category.key"
								/>
							</el-select>
						</el-form-item>
					</el-col>
				</el-row>

				<el-form-item label="组件图标" prop="icon">
					<el-input v-model="componentForm.icon" placeholder="请输入图标名称" />
				</el-form-item>

				<el-form-item label="属性配置">
					<div class="props-editor">
						<div class="props-header">
							<span>属性列表</span>
							<el-button size="small" type="primary" @click="handleAddProp">
								添加属性
							</el-button>
						</div>

						<div class="props-list">
							<div
								v-for="(prop, index) in componentForm.props"
								:key="index"
								class="prop-item"
							>
								<el-row :gutter="10">
									<el-col :span="4">
										<el-input
											v-model="prop.name"
											placeholder="属性名"
											size="small"
										/>
									</el-col>
									<el-col :span="4">
										<el-input
											v-model="prop.label"
											placeholder="显示名"
											size="small"
										/>
									</el-col>
									<el-col :span="3">
										<el-select
											v-model="prop.type"
											placeholder="类型"
											size="small"
										>
											<el-option label="字符串" value="string" />
											<el-option label="数字" value="number" />
											<el-option label="布尔值" value="boolean" />
											<el-option label="选择器" value="select" />
											<el-option label="颜色" value="color" />
											<el-option label="数组" value="array" />
											<el-option label="对象" value="object" />
										</el-select>
									</el-col>
									<el-col :span="4">
										<el-input
											v-model="prop.default"
											placeholder="默认值"
											size="small"
										/>
									</el-col>
									<el-col :span="6">
										<el-input
											v-model="prop.description"
											placeholder="描述"
											size="small"
										/>
									</el-col>
									<el-col :span="3">
										<el-button
											size="small"
											type="danger"
											:icon="Delete"
											@click="handleRemoveProp(index)"
										/>
									</el-col>
								</el-row>
							</div>
						</div>
					</div>
				</el-form-item>

				<el-form-item label="默认属性">
					<el-input
						v-model="defaultPropsText"
						type="textarea"
						:rows="4"
						placeholder="请输入 JSON 格式的默认属性"
					/>
				</el-form-item>
			</el-form>

			<template #footer>
				<el-button @click="componentDialog.visible = false">取消</el-button>
				<el-button type="primary" @click="handleSaveComponent">保存</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Edit, Delete } from '@element-plus/icons-vue';
import { useBuilderStore } from '../store/builder';
import type { CrudComponentConfig, CrudComponentProp } from '../types';
import { CrudComponentCategory } from '../types';

defineOptions({
	name: 'lowcode-components'
});

const builderStore = useBuilderStore();

// 当前激活的分类
const activeCategory = ref(CrudComponentCategory.CRUD);

// 组件分类
const categories = [
	{ key: CrudComponentCategory.CRUD, label: 'CRUD容器' },
	{ key: CrudComponentCategory.TABLE, label: '表格组件' },
	{ key: CrudComponentCategory.FORM, label: '表单组件' },
	{ key: CrudComponentCategory.SEARCH, label: '搜索组件' },
	{ key: CrudComponentCategory.ACTION, label: '操作按钮' },
	{ key: CrudComponentCategory.LAYOUT, label: '布局组件' }
];

// 组件对话框
const componentDialog = reactive({
	visible: false,
	title: '添加组件',
	mode: 'add' as 'add' | 'edit'
});

// 组件表单
const componentFormRef = ref();
const componentForm = reactive<CrudComponentConfig>({
	id: '',
	name: '',
	label: '',
	icon: '',
	category: CrudComponentCategory.LAYOUT,
	crudType: 'layout',
	props: [],
	defaultProps: {}
});

const componentFormRules = {
	id: [{ required: true, message: '请输入组件ID', trigger: 'blur' }],
	name: [{ required: true, message: '请输入组件名称', trigger: 'blur' }],
	label: [{ required: true, message: '请输入显示标签', trigger: 'blur' }],
	category: [{ required: true, message: '请选择组件分类', trigger: 'change' }]
};

// 默认属性文本
const defaultPropsText = computed({
	get: () => JSON.stringify(componentForm.defaultProps, null, 2),
	set: (value: string) => {
		try {
			componentForm.defaultProps = JSON.parse(value);
		} catch {
			// 忽略 JSON 解析错误
		}
	}
});

// 根据分类获取组件
const getComponentsByCategory = (category: string) => {
	return builderStore.availableComponents.filter(c => c.category === category);
};

// 处理分类切换
const handleCategoryChange = () => {
	// 分类切换逻辑
};

// 处理组件点击
const handleComponentClick = (component: CrudComponentConfig) => {
	// 显示组件详情或其他操作
	console.log('点击组件:', component);
};

// 添加组件
const handleAddComponent = () => {
	componentDialog.mode = 'add';
	componentDialog.title = '添加组件';
	componentDialog.visible = true;

	// 重置表单
	Object.assign(componentForm, {
		id: '',
		name: '',
		label: '',
		icon: '',
		category: activeCategory.value,
		props: [],
		defaultProps: {}
	});
};

// 编辑组件
const handleEditComponent = (component: CrudComponentConfig) => {
	componentDialog.mode = 'edit';
	componentDialog.title = '编辑组件';
	componentDialog.visible = true;

	// 填充表单
	Object.assign(componentForm, JSON.parse(JSON.stringify(component)));
};

// 删除组件
const handleDeleteComponent = async (component: CrudComponentConfig) => {
	try {
		await ElMessageBox.confirm(`确定要删除组件 "${component.label}" 吗？`, '删除确认', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		});

		// 从可用组件中移除
		const index = builderStore.availableComponents.findIndex(c => c.id === component.id);
		if (index !== -1) {
			builderStore.availableComponents.splice(index, 1);
			ElMessage.success('组件已删除');
		}
	} catch {
		// 用户取消删除
	}
};

// 保存组件
const handleSaveComponent = async () => {
	try {
		await componentFormRef.value?.validate();

		const componentData = JSON.parse(JSON.stringify(componentForm));

		if (componentDialog.mode === 'add') {
			// 检查 ID 是否重复
			const exists = builderStore.availableComponents.some(c => c.id === componentData.id);
			if (exists) {
				ElMessage.error('组件ID已存在');
				return;
			}

			// 添加到可用组件
			builderStore.availableComponents.push(componentData);
			ElMessage.success('组件添加成功');
		} else {
			// 更新组件
			const index = builderStore.availableComponents.findIndex(
				c => c.id === componentData.id
			);
			if (index !== -1) {
				builderStore.availableComponents[index] = componentData;
				ElMessage.success('组件更新成功');
			}
		}

		componentDialog.visible = false;
	} catch {
		// 验证失败
	}
};

// 添加属性
const handleAddProp = () => {
	componentForm.props.push({
		name: '',
		label: '',
		type: 'string',
		default: '',
		description: ''
	});
};

// 移除属性
const handleRemoveProp = (index: number) => {
	componentForm.props.splice(index, 1);
};
</script>

<style scoped lang="scss">
.component-library {
	padding: 20px;
	height: 100%;
	display: flex;
	flex-direction: column;

	.library-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 20px;

		h2 {
			margin: 0;
			font-size: 20px;
			font-weight: 600;
			color: #303133;
		}
	}

	.library-content {
		flex: 1;
		overflow: hidden;

		.category-tabs {
			height: 100%;

			:deep(.el-tabs__content) {
				height: calc(100% - 40px);
				overflow-y: auto;
			}

			.component-grid {
				display: grid;
				grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
				gap: 20px;
				padding: 20px 0;

				.component-card {
					border: 1px solid #e4e7ed;
					border-radius: 8px;
					padding: 16px;
					background: #fff;
					cursor: pointer;
					transition: all 0.2s;

					&:hover {
						border-color: #409eff;
						box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
					}

					.card-header {
						display: flex;
						align-items: center;
						justify-content: space-between;
						margin-bottom: 12px;

						.component-icon {
							width: 40px;
							height: 40px;
							border-radius: 6px;
							background: #f0f9ff;
							display: flex;
							align-items: center;
							justify-content: center;
							color: #409eff;

							.el-icon {
								font-size: 20px;
							}
						}

						.component-actions {
							display: flex;
							gap: 8px;
							opacity: 0;
							transition: opacity 0.2s;
						}
					}

					&:hover .component-actions {
						opacity: 1;
					}

					.card-body {
						h4 {
							margin: 0 0 4px 0;
							font-size: 16px;
							font-weight: 600;
							color: #303133;
						}

						p {
							margin: 0 0 12px 0;
							font-size: 12px;
							color: #909399;
						}

						.component-props {
							display: flex;
							flex-wrap: wrap;
							gap: 4px;
						}
					}

					&.add-card {
						border-style: dashed;
						display: flex;
						align-items: center;
						justify-content: center;
						min-height: 120px;

						.add-content {
							display: flex;
							flex-direction: column;
							align-items: center;
							gap: 8px;
							color: #909399;

							.add-icon {
								font-size: 32px;
							}
						}

						&:hover {
							border-color: #409eff;
							color: #409eff;

							.add-content {
								color: #409eff;
							}
						}
					}
				}
			}
		}
	}
}

.props-editor {
	border: 1px solid #e4e7ed;
	border-radius: 6px;
	overflow: hidden;

	.props-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 16px;
		background: #f8f9fa;
		border-bottom: 1px solid #e4e7ed;
		font-weight: 500;
	}

	.props-list {
		max-height: 300px;
		overflow-y: auto;

		.prop-item {
			padding: 12px 16px;
			border-bottom: 1px solid #f0f0f0;

			&:last-child {
				border-bottom: none;
			}
		}
	}
}
</style>
