<template>
	<cl-crud ref="Crud">
		<cl-row>
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script lang="ts" setup>
import { useCrud, useSearch, useTable, useUpsert } from '@cool-vue/crud';
import { useI18n } from 'vue-i18n';
import { useCool } from '/@/cool';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';

defineOptions({
	name: 'lowcode-pages'
});

const { t } = useI18n();
const { service } = useCool();
const router = useRouter();

// cl-table 配置
const Table = useTable({
	columns: [
		{
			type: 'selection',
			width: 60
		},
		{
			label: '页面名称',
			prop: 'name',
			minWidth: 150
		},
		{
			label: '页面标题',
			prop: 'title',
			minWidth: 200
		},
		{
			label: '描述',
			prop: 'description',
			minWidth: 250,
			showOverflowTooltip: true
		},
		{
			label: '缩略图',
			prop: 'thumbnail',
			width: 100,
			component: {
				name: 'cl-image',
				props: {
					size: 60,
					fit: 'cover'
				}
			}
		},
		{
			label: '组件数量',
			prop: 'componentCount',
			width: 100,
			formatter: (row: any) => {
				return row.components ? row.components.length : 0;
			}
		},
		{
			label: '状态',
			prop: 'status',
			width: 100,
			dict: [
				{
					label: '草稿',
					value: 0,
					type: 'warning'
				},
				{
					label: '已发布',
					value: 1,
					type: 'success'
				},
				{
					label: '已下线',
					value: 2,
					type: 'danger'
				}
			]
		},
		{
			label: '创建时间',
			prop: 'createTime',
			sortable: 'desc',
			width: 160
		},
		{
			label: '更新时间',
			prop: 'updateTime',
			width: 160
		},
		{
			type: 'op',
			buttons: [
				'edit',
				{
					label: '设计',
					type: 'primary',
					onClick: ({ scope }) => {
						handleDesign(scope.row);
					}
				},
				{
					label: '预览',
					type: 'success',
					onClick: ({ scope }) => {
						handlePreview(scope.row);
					}
				},
				{
					label: '复制',
					onClick: ({ scope }) => {
						handleCopy(scope.row);
					}
				},
				{
					label: '发布',
					type: 'warning',
					hidden: ({ scope }) => scope.row.status === 1,
					onClick: ({ scope }) => {
						handlePublish(scope.row);
					}
				},
				{
					label: '下线',
					type: 'danger',
					hidden: ({ scope }) => scope.row.status !== 1,
					onClick: ({ scope }) => {
						handleOffline(scope.row);
					}
				},
				'delete'
			]
		}
	]
});

// cl-upsert 配置
const Upsert = useUpsert({
	items: [
		{
			prop: 'name',
			label: '页面名称',
			component: { 
				name: 'el-input',
				props: {
					placeholder: '请输入页面名称'
				}
			},
			required: true
		},
		{
			prop: 'title',
			label: '页面标题',
			component: { 
				name: 'el-input',
				props: {
					placeholder: '请输入页面标题'
				}
			},
			required: true
		},
		{
			prop: 'description',
			label: '页面描述',
			component: { 
				name: 'el-input',
				props: {
					type: 'textarea',
					rows: 3,
					placeholder: '请输入页面描述'
				}
			}
		},
		{
			prop: 'thumbnail',
			label: '缩略图',
			component: { 
				name: 'cl-upload',
				props: {
					accept: 'image/*',
					limit: 1
				}
			}
		},
		{
			prop: 'status',
			label: '状态',
			value: 0,
			component: {
				name: 'el-radio-group',
				options: [
					{ label: '草稿', value: 0 },
					{ label: '已发布', value: 1 },
					{ label: '已下线', value: 2 }
				]
			}
		}
	]
});

// cl-search 配置
const Search = useSearch({
	items: [
		{
			prop: 'name',
			label: '页面名称',
			component: { name: 'el-input' }
		},
		{
			prop: 'title',
			label: '页面标题',
			component: { name: 'el-input' }
		},
		{
			prop: 'status',
			label: '状态',
			component: {
				name: 'el-select',
				options: [
					{ label: '草稿', value: 0 },
					{ label: '已发布', value: 1 },
					{ label: '已下线', value: 2 }
				]
			}
		}
	]
});

// cl-crud 配置
const Crud = useCrud(
	{
		service: service.lowcode.page
	},
	app => {
		app.refresh();
	}
);

// 方法
const handleDesign = (row: any) => {
	// 跳转到设计器页面
	router.push({
		path: '/lowcode/builder',
		query: { pageId: row.id }
	});
};

const handlePreview = (row: any) => {
	// 打开预览窗口
	const previewUrl = `/lowcode/preview/${row.id}`;
	window.open(previewUrl, '_blank');
};

const handleCopy = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要复制页面 "${row.title}" 吗？`,
			'复制确认',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'info'
			}
		);

		// 调用复制接口
		await service.lowcode.page.copy({ id: row.id });
		ElMessage.success('页面复制成功');
		
		// 刷新列表
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('复制失败');
		}
	}
};

const handlePublish = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要发布页面 "${row.title}" 吗？`,
			'发布确认',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		// 调用发布接口
		await service.lowcode.page.publish({ id: row.id });
		ElMessage.success('页面发布成功');
		
		// 刷新列表
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('发布失败');
		}
	}
};

const handleOffline = async (row: any) => {
	try {
		await ElMessageBox.confirm(
			`确定要下线页面 "${row.title}" 吗？`,
			'下线确认',
			{
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}
		);

		// 调用下线接口
		await service.lowcode.page.offline({ id: row.id });
		ElMessage.success('页面下线成功');
		
		// 刷新列表
		Crud.value?.refresh();
	} catch (error) {
		if (error !== 'cancel') {
			ElMessage.error('下线失败');
		}
	}
};
</script>
