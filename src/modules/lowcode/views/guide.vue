<template>
	<div class="guide-page">
		<div class="guide-header">
			<h1>🚀 CRUD低代码构建器使用指南</h1>
			<p>快速构建符合项目规范的CRUD页面</p>
		</div>

		<div class="guide-content">
			<el-card class="guide-card">
				<template #header>
					<h3>⚡ 快速开始</h3>
				</template>
				<ol class="guide-steps">
					<li>
						<strong>应用模板：</strong>
						<p>在左侧组件面板点击"基础CRUD"或"高级CRUD"模板，一键生成完整的CRUD结构</p>
					</li>
					<li>
						<strong>配置服务：</strong>
						<p>选中CRUD容器，在右侧属性面板配置数据服务（如：service.base.sys.user）</p>
					</li>
					<li>
						<strong>添加表格列：</strong>
						<p>从"表格列"分类中拖拽或点击列组件，自动添加到表格中</p>
					</li>
					<li>
						<strong>添加表单项：</strong>
						<p>从"表单项"分类中拖拽或点击表单组件，自动添加到表单中</p>
					</li>
					<li>
						<strong>生成代码：</strong>
						<p>点击"生成代码"按钮，获取符合项目规范的Vue代码</p>
					</li>
				</ol>
			</el-card>

			<el-card class="guide-card">
				<template #header>
					<h3>🎯 核心功能</h3>
				</template>
				<div class="feature-grid">
					<div class="feature-item">
						<div class="feature-icon">📋</div>
						<h4>可视化表格配置</h4>
						<p>拖拽添加表格列，支持文本、数字、日期、状态等多种列类型</p>
					</div>
					<div class="feature-item">
						<div class="feature-icon">📝</div>
						<h4>智能表单构建</h4>
						<p>拖拽添加表单项，支持输入框、选择器、日期选择器等组件</p>
					</div>
					<div class="feature-item">
						<div class="feature-icon">🔧</div>
						<h4>属性面板配置</h4>
						<p>选中组件后在右侧面板进行详细配置，支持列配置、表单项配置等</p>
					</div>
					<div class="feature-item">
						<div class="feature-icon">💻</div>
						<h4>标准代码生成</h4>
						<p>生成完全符合 .cursor/rules/crud.mdc 规范的Vue代码</p>
					</div>
				</div>
			</el-card>

			<el-card class="guide-card">
				<template #header>
					<h3>📚 组件说明</h3>
				</template>
				<div class="component-docs">
					<div class="doc-section">
						<h4>CRUD容器组件</h4>
						<ul>
							<li><strong>cl-crud：</strong>CRUD页面的根容器，配置数据服务和事件处理</li>
							<li><strong>cl-table：</strong>数据表格，显示列表数据</li>
							<li><strong>cl-upsert：</strong>新增编辑表单，处理数据的增改操作</li>
							<li><strong>cl-adv-search：</strong>高级搜索表单，提供复杂查询功能</li>
						</ul>
					</div>
					<div class="doc-section">
						<h4>操作按钮组件</h4>
						<ul>
							<li><strong>cl-add-btn：</strong>新增按钮</li>
							<li><strong>cl-refresh-btn：</strong>刷新按钮</li>
							<li><strong>cl-multi-delete-btn：</strong>批量删除按钮</li>
							<li><strong>cl-search-key：</strong>关键字搜索框</li>
							<li><strong>cl-import-btn / cl-export-btn：</strong>导入导出按钮</li>
						</ul>
					</div>
					<div class="doc-section">
						<h4>表格列类型</h4>
						<ul>
							<li><strong>文本列：</strong>显示普通文本数据</li>
							<li><strong>数字列：</strong>显示数字数据，右对齐</li>
							<li><strong>日期列：</strong>显示日期时间，支持排序</li>
							<li><strong>状态列：</strong>显示状态数据，支持字典转换</li>
						</ul>
					</div>
					<div class="doc-section">
						<h4>表单项类型</h4>
						<ul>
							<li><strong>输入框：</strong>文本输入</li>
							<li><strong>数字输入框：</strong>数字输入</li>
							<li><strong>选择器：</strong>下拉选择</li>
							<li><strong>日期选择器：</strong>日期时间选择</li>
							<li><strong>文本域：</strong>多行文本输入</li>
							<li><strong>开关：</strong>布尔值切换</li>
						</ul>
					</div>
				</div>
			</el-card>

			<el-card class="guide-card">
				<template #header>
					<h3>💡 使用技巧</h3>
				</template>
				<div class="tips">
					<div class="tip-item">
						<el-icon class="tip-icon"><InfoFilled /></el-icon>
						<div>
							<strong>选中目标组件：</strong>
							<p>添加表格列或表单项时，先选中对应的表格或表单组件，可以精确控制添加位置</p>
						</div>
					</div>
					<div class="tip-item">
						<el-icon class="tip-icon"><WarningFilled /></el-icon>
						<div>
							<strong>属性配置：</strong>
							<p>添加组件后，记得在属性面板中配置字段名、标签等属性，确保生成的代码正确</p>
						</div>
					</div>
					<div class="tip-item">
						<el-icon class="tip-icon"><SuccessFilled /></el-icon>
						<div>
							<strong>代码预览：</strong>
							<p>随时点击"生成代码"查看当前配置生成的Vue代码，确保符合预期</p>
						</div>
					</div>
				</div>
			</el-card>

			<div class="guide-actions">
				<el-button type="primary" size="large" @click="goToBuilder">
					开始构建 CRUD 页面
				</el-button>
				<el-button size="large" @click="goToTest">
					查看示例效果
				</el-button>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { InfoFilled, WarningFilled, SuccessFilled } from '@element-plus/icons-vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const goToBuilder = () => {
	router.push('/lowcode/builder');
};

const goToTest = () => {
	router.push('/lowcode/test');
};
</script>

<style scoped lang="scss">
.guide-page {
	padding: 20px;
	max-width: 1200px;
	margin: 0 auto;

	.guide-header {
		text-align: center;
		margin-bottom: 40px;

		h1 {
			margin: 0 0 16px 0;
			color: #303133;
			font-size: 32px;
		}

		p {
			margin: 0;
			color: #606266;
			font-size: 16px;
		}
	}

	.guide-content {
		.guide-card {
			margin-bottom: 24px;

			:deep(.el-card__header) {
				background: #f8f9fa;
				border-bottom: 1px solid #e4e7ed;

				h3 {
					margin: 0;
					color: #303133;
					font-size: 18px;
				}
			}
		}

		.guide-steps {
			padding-left: 20px;

			li {
				margin-bottom: 16px;
				line-height: 1.6;

				strong {
					color: #409eff;
				}

				p {
					margin: 4px 0 0 0;
					color: #606266;
				}
			}
		}

		.feature-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
			gap: 20px;

			.feature-item {
				text-align: center;
				padding: 20px;
				border: 1px solid #e4e7ed;
				border-radius: 8px;
				background: #fafafa;

				.feature-icon {
					font-size: 32px;
					margin-bottom: 12px;
				}

				h4 {
					margin: 0 0 8px 0;
					color: #303133;
				}

				p {
					margin: 0;
					color: #606266;
					font-size: 14px;
					line-height: 1.5;
				}
			}
		}

		.component-docs {
			.doc-section {
				margin-bottom: 24px;

				h4 {
					margin: 0 0 12px 0;
					color: #409eff;
					font-size: 16px;
				}

				ul {
					margin: 0;
					padding-left: 20px;

					li {
						margin-bottom: 8px;
						line-height: 1.5;

						strong {
							color: #303133;
						}
					}
				}
			}
		}

		.tips {
			.tip-item {
				display: flex;
				align-items: flex-start;
				gap: 12px;
				margin-bottom: 16px;
				padding: 16px;
				background: #f0f9ff;
				border: 1px solid #e1f5fe;
				border-radius: 8px;

				.tip-icon {
					color: #409eff;
					font-size: 18px;
					margin-top: 2px;
				}

				strong {
					color: #303133;
				}

				p {
					margin: 4px 0 0 0;
					color: #606266;
					font-size: 14px;
					line-height: 1.5;
				}
			}
		}

		.guide-actions {
			text-align: center;
			margin-top: 40px;

			.el-button {
				margin: 0 8px;
			}
		}
	}
}
</style>
