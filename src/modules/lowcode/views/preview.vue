<template>
	<div class="preview-page">
		<div class="preview-header">
			<div class="header-left">
				<el-button @click="goBack" :icon="ArrowLeft">返回构建器</el-button>
				<h2>{{ pageTitle }}</h2>
			</div>
			<div class="header-right">
				<el-button @click="refreshPreview" :icon="Refresh">刷新预览</el-button>
				<el-button @click="viewCode" :icon="View">查看代码</el-button>
			</div>
		</div>

		<div class="preview-content">
			<!-- 直接渲染预览组件 -->
			<repl-preview :sfc="generatedCode"></repl-preview>
			<!-- <PreviewRenderer :components="builderStore.currentPage.components" /> -->
		</div>

		<!-- 代码查看对话框 -->
		<el-dialog v-model="codeDialog.visible" title="生成的代码" width="80%" :fullscreen="codeDialog.fullscreen">
			<template #header>
				<div class="dialog-header">
					<span>生成的代码</span>
					<el-button @click="codeDialog.fullscreen = !codeDialog.fullscreen" :icon="FullScreen" circle />
				</div>
			</template>

			<el-tabs v-model="codeDialog.activeTab">
				<el-tab-pane label="Vue代码" name="vue">
					<div class="code-container">
						<pre><code class="language-vue">{{ generatedCode }}</code></pre>
					</div>
				</el-tab-pane>
				<el-tab-pane label="JSON配置" name="json">
					<div class="code-container">
						<pre><code class="language-json">{{ pageConfigJson }}</code></pre>
					</div>
				</el-tab-pane>
			</el-tabs>

			<template #footer>
				<el-button @click="copyCode">复制代码</el-button>
				<el-button @click="codeDialog.visible = false">关闭</el-button>
			</template>
		</el-dialog>
	</div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { ArrowLeft, Refresh, View, FullScreen, Loading } from '@element-plus/icons-vue';
import { useBuilderStore } from '../store/builder';
import { CrudCodeGenerator } from '../utils/crud-code-generator';
// import PreviewRenderer from '../components/preview-renderer.vue';
import ReplPreview from '../components/repl-preview.vue';

defineOptions({
	name: 'lowcode-preview'
});
const router = useRouter();
const builderStore = useBuilderStore();

const pageTitle = computed(() => builderStore.currentPage.title || '预览页面');

// 代码对话框
const codeDialog = ref({
	visible: false,
	fullscreen: false,
	activeTab: 'vue'
});

// 生成的代码
const generatedCode = computed(() => {
	const generator = new CrudCodeGenerator(builderStore.currentPage);
	return generator.generateVueCode();
});

const pageConfigJson = computed(() => {
	return JSON.stringify(builderStore.currentPage, null, 2);
});

// 返回构建器
const goBack = () => {
	router.push('/lowcode/builder');
};

// 刷新预览
const refreshPreview = () => {
	window.location.reload();
};

// 查看代码
const viewCode = () => {
	codeDialog.value.visible = true;
};

// 复制代码
const copyCode = async () => {
	try {
		const code =
			codeDialog.value.activeTab === 'vue' ? generatedCode.value : pageConfigJson.value;
		await navigator.clipboard.writeText(code);
		ElMessage.success('代码已复制到剪贴板');
	} catch (error) {
		ElMessage.error('复制失败');
	}
};
</script>

<style scoped lang="scss">
.preview-page {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;
}

.preview-header {
	background: white;
	padding: 16px 24px;
	border-bottom: 1px solid #e4e7ed;
	display: flex;
	justify-content: space-between;
	align-items: center;

	.header-left {
		display: flex;
		align-items: center;
		gap: 16px;

		h2 {
			margin: 0;
			color: #303133;
		}
	}

	.header-right {
		display: flex;
		gap: 12px;
	}
}

.preview-content {
	flex: 1;
	padding: 24px;
	overflow: auto;
	background: white;
	margin: 16px;
	border-radius: 8px;
	box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.loading-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 200px;
	color: #909399;

	.loading-icon {
		font-size: 32px;
		margin-bottom: 16px;
		animation: rotate 2s linear infinite;
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}

	to {
		transform: rotate(360deg);
	}
}

.dialog-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.code-container {
	background: #f5f5f5;
	border-radius: 4px;
	padding: 16px;
	max-height: 500px;
	overflow-y: auto;

	pre {
		margin: 0;
		white-space: pre-wrap;
		word-wrap: break-word;

		code {
			font-family: 'Courier New', monospace;
			font-size: 14px;
			line-height: 1.5;
		}
	}
}

:deep(.preview-container) {
	.el-form-item {
		margin-bottom: 20px;
	}

	.el-button {
		margin-right: 12px;
		margin-bottom: 12px;
	}

	.crud-preview {
		.crud-header {
			display: flex;
			gap: 12px;
			align-items: center;
			margin-bottom: 16px;
		}
	}

	.empty-preview {
		text-align: center;
		padding: 60px 20px;
		color: #909399;
	}
}
</style>
