<template>
	<div class="lowcode-demo">
		<div class="demo-header">
			<h1>🎨 基于 @cool-vue/crud 的低代码构建器</h1>
			<p>充分利用现有 CRUD 组件库的可视化页面设计工具</p>
		</div>

		<div class="demo-actions">
			<el-button type="primary" size="large" @click="goToBuilder"> 开始设计页面 </el-button>

			<el-button size="large" @click="goToPages"> 查看页面列表 </el-button>

			<el-button size="large" @click="goToComponents"> 管理组件库 </el-button>
		</div>

		<div class="demo-features">
			<h2>核心特性</h2>
			<div class="feature-grid">
				<div class="feature-card">
					<div class="feature-icon">🗂️</div>
					<h3>CRUD 容器</h3>
					<p>基于 cl-crud 组件，提供完整的 CRUD 功能框架</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">📊</div>
					<h3>数据表格</h3>
					<p>可视化配置 cl-table 组件，支持列定义、排序、分页等</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">📝</div>
					<h3>表单设计</h3>
					<p>拖拽式设计 cl-upsert 表单，支持各种表单控件</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">🔍</div>
					<h3>搜索组件</h3>
					<p>快速配置 cl-search 搜索功能，支持多种搜索条件</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">🔘</div>
					<h3>操作按钮</h3>
					<p>内置新增、刷新、删除等常用操作按钮组件</p>
				</div>
				<div class="feature-card">
					<div class="feature-icon">💾</div>
					<h3>代码生成</h3>
					<p>自动生成基于 @cool-vue/crud 的完整页面代码</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';

defineOptions({
	name: 'lowcode-demo'
});

const router = useRouter();

const goToBuilder = () => {
	router.push('/lowcode/builder');
};

const goToPages = () => {
	router.push('/lowcode/pages');
};

const goToComponents = () => {
	router.push('/lowcode/components');
};
</script>

<style scoped lang="scss">
.lowcode-demo {
	padding: 40px;
	max-width: 1200px;
	margin: 0 auto;

	.demo-header {
		text-align: center;
		margin-bottom: 60px;

		h1 {
			font-size: 36px;
			font-weight: 700;
			color: #303133;
			margin-bottom: 16px;
		}

		p {
			font-size: 18px;
			color: #606266;
			margin: 0;
		}
	}

	.demo-features {
		margin-bottom: 60px;

		.feature-grid {
			display: grid;
			grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
			gap: 24px;

			.feature-card {
				background: #fff;
				border: 1px solid #e4e7ed;
				border-radius: 12px;
				padding: 32px 24px;
				text-align: center;
				transition: all 0.3s;

				&:hover {
					border-color: #409eff;
					box-shadow: 0 8px 24px rgba(64, 158, 255, 0.1);
					transform: translateY(-4px);
				}

				.feature-icon {
					font-size: 48px;
					margin-bottom: 16px;
				}

				h3 {
					font-size: 20px;
					font-weight: 600;
					color: #303133;
					margin-bottom: 12px;
				}

				p {
					font-size: 14px;
					color: #606266;
					line-height: 1.6;
					margin: 0;
				}
			}
		}
	}

	.demo-actions {
		text-align: center;
		margin-bottom: 60px;

		.el-button {
			margin: 0 12px;
		}
	}

	.demo-preview {
		margin-bottom: 60px;

		h2 {
			font-size: 28px;
			font-weight: 600;
			color: #303133;
			text-align: center;
			margin-bottom: 32px;
		}

		.preview-tabs {
			.preview-image {
				display: flex;
				align-items: flex-start;
				gap: 32px;

				img {
					max-width: 60%;
					border-radius: 8px;
					box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
				}

				.preview-description {
					flex: 1;

					h4 {
						font-size: 20px;
						font-weight: 600;
						color: #303133;
						margin-bottom: 16px;
					}

					ul {
						padding-left: 20px;

						li {
							margin-bottom: 8px;
							color: #606266;
							line-height: 1.6;
						}
					}
				}
			}

			.component-showcase,
			.code-showcase {
				h4 {
					font-size: 20px;
					font-weight: 600;
					color: #303133;
					margin-bottom: 24px;
				}

				.component-categories {
					.category {
						margin-bottom: 24px;

						h5 {
							font-size: 16px;
							font-weight: 600;
							color: #409eff;
							margin-bottom: 12px;
						}

						.component-list {
							display: flex;
							flex-wrap: wrap;
							gap: 8px;

							.component-tag {
								background: #f0f9ff;
								color: #409eff;
								padding: 4px 12px;
								border-radius: 16px;
								font-size: 12px;
								border: 1px solid #b3d8ff;
							}
						}
					}
				}

				.code-example {
					background: #f8f9fa;
					border: 1px solid #e4e7ed;
					border-radius: 8px;
					padding: 20px;
					margin-bottom: 16px;

					pre {
						margin: 0;
						font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
						font-size: 12px;
						line-height: 1.5;
						color: #303133;
					}
				}
			}
		}
	}

	.demo-workflow {
		h2 {
			font-size: 28px;
			font-weight: 600;
			color: #303133;
			text-align: center;
			margin-bottom: 32px;
		}

		.workflow-steps {
			display: flex;
			align-items: center;
			justify-content: center;
			flex-wrap: wrap;
			gap: 24px;

			.step {
				display: flex;
				flex-direction: column;
				align-items: center;
				text-align: center;
				max-width: 200px;

				.step-number {
					width: 48px;
					height: 48px;
					border-radius: 50%;
					background: #409eff;
					color: white;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 20px;
					font-weight: 600;
					margin-bottom: 16px;
				}

				.step-content {
					h4 {
						font-size: 16px;
						font-weight: 600;
						color: #303133;
						margin-bottom: 8px;
					}

					p {
						font-size: 14px;
						color: #606266;
						line-height: 1.5;
						margin: 0;
					}
				}
			}

			.step-arrow {
				font-size: 24px;
				color: #409eff;
				font-weight: bold;
			}
		}
	}
}

@media (max-width: 768px) {
	.lowcode-demo {
		padding: 20px;

		.demo-header h1 {
			font-size: 28px;
		}

		.feature-grid {
			grid-template-columns: 1fr;
		}

		.workflow-steps {
			flex-direction: column;

			.step-arrow {
				transform: rotate(90deg);
			}
		}

		.preview-image {
			flex-direction: column;

			img {
				max-width: 100%;
			}
		}
	}
}
</style>
