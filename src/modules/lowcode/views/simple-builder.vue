<template>
	<div class="lowcode-builder">
		<!-- 顶部工具栏 -->
		<div class="builder-header">
			<div class="header-left">
				<h2>低代码页面构建器 (简洁版)</h2>
			</div>

			<div class="header-center">
				<el-button :icon="Document" @click="handleSavePage"> 保存 </el-button>
				<el-button
					type="danger"
					:disabled="builderStore.currentPage.components.length === 0"
					@click="handleClearPage"
					title="清空画布"
				>
					清空
				</el-button>
			</div>

			<div class="header-right">
				<el-button :icon="View" type="primary" @click="handlePreview"> 预览页面 </el-button>
			</div>
		</div>

		<!-- 主体区域 -->
		<div class="builder-body">
			<!-- 左侧组件面板 -->
			<component-panel />

			<!-- 中间画布区域 -->
			<canvas-panel />

			<!-- 右侧属性面板 -->
			<property-panel />
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRouter } from 'vue-router';
import { Document, View } from '@element-plus/icons-vue';
import { useBuilderStore } from '../store/builder';
import ComponentPanel from '../components/simple/component-panel/index.vue';
import CanvasPanel from '../components/simple/canvas-panel/index.vue';
import PropertyPanel from '../components/simple/property-panel/index.vue';

defineOptions({
	name: 'lowcode-simple-builder'
});

const router = useRouter();
const builderStore = useBuilderStore();

// 方法
const handleSavePage = async () => {
	try {
		const pageConfig = builderStore.exportPageConfig();
		console.log('保存页面配置:', pageConfig);
		ElMessage.success('页面已保存');
	} catch (error) {
		ElMessage.error('保存失败');
	}
};

const handlePreview = () => {
	// 跳转到预览页面
	router.push('/lowcode/preview');
};

const handleClearPage = () => {
	ElMessageBox.confirm('确定要清空画布吗？此操作不可恢复。', '确认清空', {
		confirmButtonText: '确定',
		cancelButtonText: '取消',
		type: 'warning'
	})
		.then(() => {
			builderStore.clearPage();
			ElMessage.success('画布已清空');
		})
		.catch(() => {
			// 用户取消操作
		});
};

// 生命周期
onMounted(() => {
	builderStore.init();
});
</script>

<style scoped lang="scss">
.lowcode-builder {
	height: 100vh;
	display: flex;
	flex-direction: column;
	background: #f5f5f5;

	.builder-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 12px 20px;
		background: #fff;
		border-bottom: 1px solid #e4e7ed;
		box-shadow: 0 1px 4px rgba(0, 0, 0, 0.04);

		.header-left h2 {
			margin: 0;
			font-size: 18px;
			font-weight: 600;
			color: #303133;
		}

		.header-center,
		.header-right {
			display: flex;
			align-items: center;
			gap: 12px;
		}
	}

	.builder-body {
		flex: 1;
		display: flex;
		overflow: hidden;
	}
}
</style>
