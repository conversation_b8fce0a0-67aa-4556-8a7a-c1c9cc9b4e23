import { type ModuleConfig } from '/@/cool';
export default (): ModuleConfig => {
	return {
		// 页面配置
		pages: [
			{
				name: 'lowcode-guide',
				path: '/lowcode/guide',
				component: () => import('./views/guide.vue'),
				meta: {
					label: '使用指南'
				}
			},
			{
				name: 'lowcode-demo',
				path: '/lowcode/demo',
				component: () => import('./views/demo.vue'),
				meta: {
					label: '功能演示'
				}
			},
			{
				name: 'lowcode-builder',
				path: '/lowcode/builder',
				component: () => import('./views/builder.vue'),
				meta: {
					label: '页面构建器'
				}
			},
			{
				name: 'lowcode-preview',
				path: '/lowcode/preview',
				component: () => import('./views/preview.vue'),
				meta: {
					label: '预览页面',
					hidden: true // 隐藏在菜单中
				}
			},
			{
				name: 'lowcode-pages',
				path: '/lowcode/pages',
				component: () => import('./views/pages.vue'),
				meta: {
					label: '页面管理'
				}
			},
			{
				name: 'lowcode-components',
				path: '/lowcode/components',
				component: () => import('./views/components.vue'),
				meta: {
					label: '组件库'
				}
			},
			{
				name: 'lowcode-test',
				path: '/lowcode/test',
				component: () => import('./__test__/test-code-generation.vue'),
				meta: {
					label: '组件库'
				}
			},
			{
				name: 'lowcode-simple-builder',
				path: '/lowcode/simple-builder',
				component: () => import('./views/simple-builder.vue'),
				meta: {
					label: '简洁构建器'
				}
			}
		]
	};
};
