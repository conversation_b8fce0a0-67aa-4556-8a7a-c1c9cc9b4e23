<template>
	<div class="preview-container">
		<component :is="dynamicComponent" />
	</div>
</template>
<script setup lang="ts">
import { defineAsyncComponent, ref } from 'vue';
import { sfcPreview } from '../components/preview';

const dynamicComponent = ref();

const code = `
<template>
	<cl-crud ref="Crud">
		<cl-row>
		测试
			<!-- 刷新按钮 -->
			<cl-refresh-btn />
			<!-- 新增按钮 -->
			<cl-add-btn />
			<!-- 删除按钮 -->
			<cl-multi-delete-btn />
			<cl-flex1 />
			<!-- 条件搜索 -->
			<cl-search ref="Search" />
		</cl-row>

		<cl-row>
			<!-- 数据表格 -->
			<cl-table ref="Table" />
		</cl-row>

		<cl-row>
			<cl-flex1 />
			<!-- 分页控件 -->
			<cl-pagination />
		</cl-row>

		<!-- 新增、编辑 -->
		<cl-upsert ref="Upsert" />
	</cl-crud>
</template>

<script setup lang="ts">
import { useCrud, useTable, useUpsert } from '@cool-vue/crud';
import { ref, onMounted } from 'vue';
import { useDict } from '/$/dict';
import { useCool } from '/@/cool';
import { ElMessage } from 'element-plus';

const { service } = useCool();
const { dict } = useDict();

// cl-crud 配置
const Crud = useCrud(
	{
		service: 'test'
	},
	app => {
		app.refresh({
			size: 10
		});
	}
);

// cl-table 配置
const Table = useTable({
	autoHeight: true,
	contextMenu: ['refresh', 'check', 'delete'],

	columns: [
		{
			label: '订单编号',
			prop: 'orderNo',
			width: 180,
			sortable: true
		},
		{
			label: '创建时间',
			prop: 'createTime',
			width: 160,
			sortable: true
		},
		{
			label: '客户名称',
			prop: 'customerName',
			minWidth: 120
		},
		{
			label: '订单金额',
			prop: 'totalAmount',
			width: 120
		},
		{
			label: '状态',
			prop: 'status',
			width: 100,
			dict: dict.get('[object Object],[object Object],[object Object]')
		}
	]
});

// cl-upsert 配置
const Upsert = useUpsert({
	items: [
		{
			label: '订单编号',
			prop: 'orderNo',
			required: true,
			component: { name: 'el-input', props: { disabled: true } }
		},
		{
			label: '客户选择',
			prop: 'customerId',
			required: true,
			component: {
				name: 'el-select',
				props: { filterable: true, remote: true, 'remote-method': 'getCustomers' }
			}
		},
		{}
	]
});

// 刷新列表
function refresh(params?: any) {
	Crud.value?.refresh(params);
}

// 页面加载时自动刷新
onMounted(() => {
	refresh();
});
<\/script>

<style scoped>
.crud-page {
	padding: 20px;
}
</style>
`;

dynamicComponent.value = defineAsyncComponent({
	loader: async () => {
		return await sfcPreview(code);
	},
	loadingComponent: {
		template: '<div>加载中...</div>'
	},
	errorComponent: {
		template: '<div>加载失败</div>'
	},
	delay: 300,
	onError: error => {
		console.error('组件加载错误:', error);
	}
});
</script>

<style>
.preview-container {
	width: 100vw;
	height: 100vh;
}
</style>
