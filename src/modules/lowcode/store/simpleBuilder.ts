import { defineStore } from 'pinia';

interface Component {
	id: string;
	type: string;
	props: Record<string, any>;
}

export const useSimpleBuilderStore = defineStore('simpleBuilder', {
	state: () => ({
		components: [] as Component[],
		selectedComponentId: null as string | null
	}),

	getters: {
		selectedComponent(state): Component | undefined {
			return state.components.find(c => c.id === state.selectedComponentId);
		},

		pageConfig(state) {
			return {
				name: 'SimpleGeneratedPage',
				title: '一个简洁的生成页面',
				description: '由简洁构建器生成',
				components: state.components.map(c => ({
					...c,
					componentId: c.id,
					label: c.type, // 使用 type 作为临时 label
					style: {} // 提供一个空的 style 对象
				}))
			};
		}
	},

	actions: {
		addComponent(type: string) {
			let props: Record<string, any> = {};

			if (type === 'text') {
				props = { text: '这是一个文本' };
			} else if (type === 'button') {
				props = { text: '按钮' };
			} else if (type === 'input') {
				props = { placeholder: '请输入内容' };
			}

			this.components.push({
				id: Date.now().toString(),
				type,
				props
			});
		},

		selectComponent(id: string) {
			this.selectedComponentId = id;
		},

		updateComponentProps(payload: { id: string; props: { [key: string]: any } }) {
			const component = this.components.find(c => c.id === payload.id);
			if (component) {
				component.props = { ...component.props, ...payload.props };
			}
		}
	}
});
