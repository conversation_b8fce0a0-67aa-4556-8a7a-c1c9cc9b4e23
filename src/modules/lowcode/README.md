# 🎨 拖拽式低代码页面构建器

基于现有 CRUD 系统构建的可视化页面设计工具，支持拖拽式组件布局、实时预览和代码生成。

## ✨ 核心特性

### 🧩 组件化设计
- **丰富的预置组件库**：布局、表单、数据展示、反馈等多种组件
- **拖拽式操作**：直观的拖拽交互，快速构建页面布局
- **组件嵌套**：支持容器组件嵌套，构建复杂布局结构

### ⚙️ 可视化配置
- **属性面板**：实时配置组件属性，支持多种数据类型
- **样式编辑**：可视化样式配置，支持布局、间距、颜色等
- **JSON 编辑器**：复杂数据结构的 JSON 编辑支持

### 📱 响应式设计
- **多设备适配**：自动生成响应式布局代码
- **实时预览**：所见即所得的编辑体验
- **缩放功能**：支持画布缩放，适应不同屏幕尺寸

### 💾 代码生成
- **Vue 组件导出**：一键生成完整的 Vue 组件代码
- **JSON 配置导出**：导出页面配置文件，支持版本管理
- **模板系统**：保存常用布局为模板，提高开发效率

## 🏗️ 系统架构

```
src/modules/lowcode/
├── components/           # 核心组件
│   ├── component-panel/  # 组件面板
│   ├── canvas-panel/     # 画布面板
│   ├── property-panel/   # 属性面板
│   └── ...
├── store/               # 状态管理
│   └── builder.ts       # 构建器状态
├── types/               # 类型定义
│   └── index.ts         # 核心类型
├── views/               # 页面视图
│   ├── builder.vue      # 构建器主页面
│   ├── pages.vue        # 页面管理
│   ├── components.vue   # 组件库管理
│   └── demo.vue         # 功能演示
├── service/             # 服务层
│   └── index.ts         # API 服务
└── config.ts            # 模块配置
```

## 🚀 快速开始

### 1. 访问演示页面
```
/lowcode/demo
```

### 2. 开始设计页面
```
/lowcode/builder
```

### 3. 管理页面
```
/lowcode/pages
```

### 4. 组件库管理
```
/lowcode/components
```

## 📋 使用流程

### 1. 选择组件
从左侧组件面板选择需要的组件：
- 📐 **布局组件**：容器、行、列
- 📝 **表单组件**：输入框、按钮、选择器
- 📊 **数据展示**：表格、列表、卡片
- 💬 **反馈组件**：消息、对话框、加载

### 2. 拖拽布局
- 将组件从组件面板拖拽到画布中
- 支持组件嵌套和位置调整
- 实时显示拖拽指示器

### 3. 配置属性
在右侧属性面板配置：
- **基本属性**：组件特有的功能属性
- **样式属性**：布局、间距、颜色、边框等
- **事件处理**：组件交互事件配置

### 4. 预览导出
- **实时预览**：切换预览模式查看最终效果
- **代码导出**：生成 Vue 组件代码
- **保存页面**：保存页面配置到数据库

## 🔧 技术实现

### 核心技术栈
- **Vue 3** + **TypeScript**：现代化前端框架
- **Element Plus**：UI 组件库
- **Pinia**：状态管理
- **VueDraggable**：拖拽功能实现

### 关键特性实现

#### 1. 拖拽系统
```typescript
// 拖拽开始
const handleDragStart = (component: ComponentConfig, event: DragEvent) => {
  event.dataTransfer?.setData('application/json', JSON.stringify({
    type: 'component',
    data: component
  }));
};

// 拖拽放置
const handleDrop = (event: DragEvent) => {
  const data = JSON.parse(event.dataTransfer?.getData('application/json'));
  builderStore.addComponent(data.data);
};
```

#### 2. 组件渲染
```typescript
// 动态组件渲染
const getComponentTag = () => {
  const componentMap = {
    container: 'div',
    row: 'el-row',
    col: 'el-col',
    input: 'el-input',
    button: 'el-button'
  };
  return componentMap[component.componentId] || 'div';
};
```

#### 3. 状态管理
```typescript
// Pinia Store
export const useBuilderStore = defineStore('lowcode-builder', () => {
  const currentPage = ref<PageConfig>({
    name: 'untitled',
    title: '未命名页面',
    components: []
  });
  
  const addComponent = (config: ComponentConfig) => {
    // 添加组件逻辑
  };
  
  return { currentPage, addComponent };
});
```

## 🎯 内置组件

### 布局组件
- **Container（容器）**：基础容器组件，支持内边距、背景色等配置
- **Row（行）**：栅格行组件，支持间距和对齐方式配置
- **Col（列）**：栅格列组件，支持跨度和偏移配置

### 表单组件
- **Input（输入框）**：文本输入组件，支持占位符、禁用、清空等
- **Button（按钮）**：按钮组件，支持类型、尺寸、禁用等配置
- **Select（选择器）**：下拉选择组件，支持选项配置

### 数据展示
- **Table（表格）**：数据表格组件，支持列配置、边框、斑马纹等
- **Text（文本）**：文本显示组件，支持字体、颜色、对齐等配置

## 🔄 扩展开发

### 添加自定义组件

1. **定义组件配置**
```typescript
const customComponent: ComponentConfig = {
  id: 'custom-chart',
  name: 'CustomChart',
  label: '自定义图表',
  icon: 'icon-chart',
  category: ComponentCategory.DATA,
  props: [
    { name: 'data', label: '数据源', type: 'array', default: [] },
    { name: 'type', label: '图表类型', type: 'select', options: [...] }
  ],
  defaultProps: { data: [], type: 'line' }
};
```

2. **注册组件**
```typescript
builderStore.availableComponents.push(customComponent);
```

3. **实现渲染逻辑**
在 `component-renderer.vue` 中添加对应的渲染逻辑。

### 扩展属性类型

在 `property-panel/index.vue` 中添加新的属性编辑器：

```vue
<!-- 自定义属性编辑器 -->
<custom-property-editor
  v-else-if="prop.type === 'custom'"
  :model-value="selectedComponent.props[prop.name]"
  @change="handlePropChange(prop.name, $event)"
/>
```

## 📊 数据流程

```mermaid
graph TD
    A[组件面板] --> B[拖拽组件]
    B --> C[画布渲染]
    C --> D[选择组件]
    D --> E[属性配置]
    E --> F[实时更新]
    F --> C
    C --> G[预览模式]
    C --> H[代码导出]
    C --> I[保存页面]
```

## 🎨 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│                        顶部工具栏                            │
│  [撤销] [重做] [复制] [删除]    页面标题    [编辑] [预览]     │
├─────────────┬─────────────────────────────┬─────────────────┤
│             │                             │                 │
│   组件面板   │          画布区域            │    属性面板      │
│             │                             │                 │
│  ┌─────────┐│  ┌─────────────────────────┐ │  ┌─────────────┐│
│  │ 布局组件 ││  │                         │ │  │ 基本信息     ││
│  │ ├容器   ││  │       页面画布           │ │  │ ├组件ID     ││
│  │ ├行     ││  │                         │ │  │ ├组件名称   ││
│  │ └列     ││  │   [拖拽组件到此处]       │ │  │             ││
│  │         ││  │                         │ │  │ 属性配置     ││
│  │ 表单组件 ││  │                         │ │  │ ├文本内容   ││
│  │ ├输入框  ││  │                         │ │  │ ├字体大小   ││
│  │ ├按钮   ││  │                         │ │  │ └颜色      ││
│  │ └选择器  ││  │                         │ │  │             ││
│  │         ││  │                         │ │  │ 样式配置     ││
│  │ 数据展示 ││  │                         │ │  │ ├宽度      ││
│  │ ├表格   ││  │                         │ │  │ ├高度      ││
│  │ └文本   ││  │                         │ │  │ └边距      ││
│  └─────────┘│  └─────────────────────────┘ │  └─────────────┘│
│             │                             │                 │
└─────────────┴─────────────────────────────┴─────────────────┘
```

## 🚀 未来规划

- [ ] **模板市场**：提供更多预制模板
- [ ] **组件市场**：支持第三方组件扩展
- [ ] **协作功能**：多人协作编辑支持
- [ ] **版本管理**：页面版本控制和回滚
- [ ] **主题系统**：支持多主题切换
- [ ] **国际化**：多语言支持
- [ ] **移动端适配**：移动端编辑器支持

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🎨 基础拖拽式页面构建器
- 📦 内置常用组件库
- 💾 代码生成和导出功能
- 📱 响应式设计支持

---

**基于现有 CRUD 系统构建，充分利用了项目的组件化架构和类型安全特性，为快速页面开发提供了强大的可视化工具。**
