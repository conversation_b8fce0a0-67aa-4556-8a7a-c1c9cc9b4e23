import { createApp } from 'vue';
import App from './App.vue';
import { bootstrap } from './cool';
import TDesignChat from '@tdesign-vue-next/chat'; // 引入chat组件
import '@tdesign-vue-next/chat/es/style/index.css'; // 引入chat组件的少量全局样式变量

const app = createApp(App);

// 启动
bootstrap(app)
	.then(() => {
		app.use(TDesignChat);
		app.mount('#app');
	})
	.catch(err => {
		console.error('COOL-ADMIN 启动失败', err);
	});
