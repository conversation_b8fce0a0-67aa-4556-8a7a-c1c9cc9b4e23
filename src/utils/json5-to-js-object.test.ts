import { describe, it, expect } from 'vitest';
import { json5ToJsObject } from './json5-to-js-object';

describe('json5ToJsObject', () => {
	it('should parse a valid JSON5 string', () => {
		const json5String = `{
		    // comments
		    key: 'value',
		    unquoted: 'and you can quote me on that',
		    singleQuotes: 'I can use "double quotes" here',
		    lineBreaks: 'Look, Mom! \\nNo \\n!',
		    hexadecimal: 0xdecaf,
		    leadingDecimalPoint: .8675309,
		    andTrailing: 8675309.,
		    positiveSign: +1,
		    trailingComma: 'in objects',
		    andIn: ['arrays'],
		  }`;
		const expectedObject = {
			key: 'value',
			unquoted: 'and you can quote me on that',
			singleQuotes: 'I can use "double quotes" here',
			lineBreaks: 'Look, Mom! \nNo \n!',
			hexadecimal: 0xdecaf,
			leadingDecimalPoint: 0.8675309,
			andTrailing: 8675309,
			positiveSign: 1,
			trailingComma: 'in objects',
			andIn: ['arrays']
		};
		expect(json5ToJsObject(json5String)).toEqual(expectedObject);
	});

	it('should return null for an invalid JSON5 string', () => {
		const json5String = '{ key: value }'; // value is not quoted
		expect(json5ToJsObject(json5String)).toBeNull();
	});
});
