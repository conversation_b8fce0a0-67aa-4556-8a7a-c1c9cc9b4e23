/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
  export interface GlobalDirectives {
    vInfiniteScroll: typeof import('vue-devui/infinite-scroll/index.es.js')['InfiniteScrollDirective']
    vLoading: typeof import('vue-devui/loading/index.es.js')['LoadingDirective']
    vPermission: typeof import('vue-devui/permission/index.es.js')['PermissionDirective']
  }
}
